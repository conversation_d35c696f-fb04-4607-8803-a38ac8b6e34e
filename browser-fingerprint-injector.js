/**
 * 浏览器指纹注入脚本生成器
 * 生成 JavaScript 代码来虚拟化浏览器指纹
 */

const fs = require('fs');
const path = require('path');

class BrowserFingerprintInjector {
    /**
     * 生成浏览器指纹注入脚本
     */
    static generateInjectionScript(virtualIdentity) {
        const script = `
// 浏览器指纹虚拟化注入脚本
// 自动生成于: ${new Date().toISOString()}

(function() {
    console.log('🎭 开始虚拟化浏览器指纹...');
    
    const virtualData = ${JSON.stringify(virtualIdentity, null, 4)};
    
    // 1. 虚拟化 Screen 对象
    if (typeof screen !== 'undefined') {
        Object.defineProperty(screen, 'width', {
            get: function() { return virtualData.screen.width; },
            configurable: true
        });
        
        Object.defineProperty(screen, 'height', {
            get: function() { return virtualData.screen.height; },
            configurable: true
        });
        
        Object.defineProperty(screen, 'availWidth', {
            get: function() { return virtualData.screen.availWidth; },
            configurable: true
        });
        
        Object.defineProperty(screen, 'availHeight', {
            get: function() { return virtualData.screen.availHeight; },
            configurable: true
        });
        
        Object.defineProperty(screen, 'colorDepth', {
            get: function() { return virtualData.screen.colorDepth; },
            configurable: true
        });
        
        Object.defineProperty(screen, 'pixelDepth', {
            get: function() { return virtualData.screen.pixelDepth; },
            configurable: true
        });
        
        console.log('✅ Screen 对象已虚拟化:', virtualData.screen.width + 'x' + virtualData.screen.height);
    }
    
    // 2. 虚拟化 Navigator 对象
    if (typeof navigator !== 'undefined') {
        Object.defineProperty(navigator, 'platform', {
            get: function() { return virtualData.platform.platform; },
            configurable: true
        });
        
        Object.defineProperty(navigator, 'userAgent', {
            get: function() { return virtualData.platform.userAgent; },
            configurable: true
        });
        
        Object.defineProperty(navigator, 'hardwareConcurrency', {
            get: function() { return virtualData.platform.hardwareConcurrency; },
            configurable: true
        });
        
        // 虚拟化语言设置
        Object.defineProperty(navigator, 'language', {
            get: function() { return 'zh-CN'; },
            configurable: true
        });
        
        Object.defineProperty(navigator, 'languages', {
            get: function() { return ['zh-CN', 'zh', 'en']; },
            configurable: true
        });
        
        console.log('✅ Navigator 对象已虚拟化');
    }
    
    // 3. 虚拟化 WebGL 指纹
    if (typeof WebGLRenderingContext !== 'undefined') {
        const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            if (parameter === this.RENDERER) {
                return virtualData.webgl.renderer;
            }
            if (parameter === this.VENDOR) {
                return virtualData.webgl.vendor;
            }
            return originalGetParameter.call(this, parameter);
        };
        
        console.log('✅ WebGL 指纹已虚拟化:', virtualData.webgl.renderer);
    }
    
    // 4. 虚拟化 Canvas 指纹
    if (typeof HTMLCanvasElement !== 'undefined') {
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
        
        HTMLCanvasElement.prototype.toDataURL = function() {
            const result = originalToDataURL.apply(this, arguments);
            // 添加基于虚拟身份的随机噪声
            const noise = virtualData.machineId.substring(0, 8);
            return result.replace(/data:image\\/png;base64,/, \`data:image/png;base64,\${noise}\`);
        };
        
        CanvasRenderingContext2D.prototype.getImageData = function() {
            const result = originalGetImageData.apply(this, arguments);
            // 轻微修改像素数据
            if (result.data && result.data.length > 10) {
                const seedValue = parseInt(virtualData.machineId.substring(0, 8), 16) % 256;
                result.data[0] = (result.data[0] + seedValue) % 256;
                result.data[1] = (result.data[1] + seedValue) % 256;
            }
            return result;
        };
        
        console.log('✅ Canvas 指纹已虚拟化');
    }
    
    // 5. 虚拟化时区
    if (typeof Intl !== 'undefined' && Intl.DateTimeFormat) {
        const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
        Intl.DateTimeFormat.prototype.resolvedOptions = function() {
            const options = originalResolvedOptions.call(this);
            options.timeZone = virtualData.timezone;
            return options;
        };
        
        // 虚拟化 Date 对象的时区偏移
        const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
        Date.prototype.getTimezoneOffset = function() {
            // 根据虚拟时区返回相应的偏移量
            const timezoneOffsets = {
                'Asia/Shanghai': -480,
                'America/New_York': 300,
                'Europe/London': 0,
                'Asia/Tokyo': -540,
                'Australia/Sydney': -660,
                'America/Los_Angeles': 480,
                'Europe/Paris': -60,
                'Asia/Seoul': -540
            };
            return timezoneOffsets[virtualData.timezone] || originalGetTimezoneOffset.call(this);
        };
        
        console.log('✅ 时区已虚拟化:', virtualData.timezone);
    }
    
    // 6. 虚拟化字体检测
    if (typeof document !== 'undefined') {
        // 创建虚拟字体检测结果
        const virtualFonts = virtualData.fonts;
        
        // 拦截字体检测方法
        const originalOffsetWidth = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetWidth');
        const originalOffsetHeight = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetHeight');
        
        if (originalOffsetWidth && originalOffsetHeight) {
            Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
                get: function() {
                    if (this.style && this.style.fontFamily) {
                        // 如果是字体检测元素，返回虚拟结果
                        const fontFamily = this.style.fontFamily.toLowerCase();
                        const hasFont = virtualFonts.some(font => 
                            fontFamily.includes(font.toLowerCase())
                        );
                        if (hasFont) {
                            return 100 + Math.floor(Math.random() * 20); // 虚拟宽度
                        }
                    }
                    return originalOffsetWidth.get.call(this);
                },
                configurable: true
            });
        }
        
        console.log('✅ 字体检测已虚拟化，可用字体数量:', virtualFonts.length);
    }
    
    // 7. 虚拟化音频指纹
    if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
        const AudioContextClass = AudioContext || webkitAudioContext;
        const originalCreateOscillator = AudioContextClass.prototype.createOscillator;
        
        AudioContextClass.prototype.createOscillator = function() {
            const oscillator = originalCreateOscillator.call(this);
            const originalStart = oscillator.start;
            
            oscillator.start = function() {
                // 添加基于虚拟身份的微小频率偏移
                const offset = (parseInt(virtualData.machineId.substring(8, 16), 16) % 100) / 10000;
                if (this.frequency && this.frequency.value) {
                    this.frequency.value += offset;
                }
                return originalStart.apply(this, arguments);
            };
            
            return oscillator;
        };
        
        console.log('✅ 音频指纹已虚拟化');
    }
    
    // 8. 设置全局虚拟身份标识
    if (typeof window !== 'undefined') {
        window.__VIRTUAL_IDENTITY__ = {
            id: virtualData.machineId.substring(0, 16),
            timestamp: Date.now(),
            version: '1.0.0'
        };
    }
    
    console.log('🎉 浏览器指纹虚拟化完成！');
    console.log('🆔 虚拟身份ID:', virtualData.machineId.substring(0, 16) + '...');
    console.log('📊 虚拟化项目: Screen, Navigator, WebGL, Canvas, Timezone, Fonts, Audio');
    
})();
`;

        return script;
    }

    /**
     * 保存注入脚本到文件
     */
    static saveInjectionScript(virtualIdentity, outputPath) {
        const script = this.generateInjectionScript(virtualIdentity);
        fs.writeFileSync(outputPath, script);
        return outputPath;
    }
}

module.exports = BrowserFingerprintInjector;
