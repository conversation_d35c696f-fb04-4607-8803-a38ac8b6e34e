/**
 * Augment 一键重置工具
 * 清理 + 生成新身份 + 启动 VSCode 一步完成
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { spawn } = require('child_process');

class AugmentReset {
    constructor() {
        this.vscodeUserDataDir = path.join(process.env.APPDATA, 'Code');
        this.vscodeInstallPath = 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code';
        this.vscodeExe = path.join(this.vscodeInstallPath, 'Code.exe');
        this.augmentExtensionId = 'augment.vscode-augment';
    }

    /**
     * 生成新的机器 ID
     */
    generateNewMachineId() {
        return crypto.randomBytes(32).toString('hex');
    }

    /**
     * 更新机器 ID
     */
    updateMachineId() {
        const machineIdFile = path.join(this.vscodeUserDataDir, 'machineid');
        const backupFile = path.join(this.vscodeUserDataDir, 'machineid.original');
        
        // 备份原始 ID（只备份一次）
        if (fs.existsSync(machineIdFile) && !fs.existsSync(backupFile)) {
            fs.copyFileSync(machineIdFile, backupFile);
            console.log('💾 原始机器 ID 已备份');
        }
        
        // 生成新 ID
        const newMachineId = this.generateNewMachineId();
        
        // 确保目录存在
        if (!fs.existsSync(this.vscodeUserDataDir)) {
            fs.mkdirSync(this.vscodeUserDataDir, { recursive: true });
        }
        
        // 写入新 ID
        fs.writeFileSync(machineIdFile, newMachineId);
        console.log('🆔 新机器 ID:', newMachineId.substring(0, 16) + '...');
        
        return newMachineId;
    }

    /**
     * 清理 Augment 数据
     */
    cleanAugmentData() {
        let cleanedItems = 0;

        // 清理全局存储
        const globalStorageDir = path.join(this.vscodeUserDataDir, 'User', 'globalStorage');
        const augmentStorageDir = path.join(globalStorageDir, this.augmentExtensionId);
        
        if (fs.existsSync(augmentStorageDir)) {
            try {
                fs.rmSync(augmentStorageDir, { recursive: true, force: true });
                console.log('✅ 清理 Augment 全局存储');
                cleanedItems++;
            } catch (error) {
                console.warn('⚠️ 清理全局存储失败:', error.message);
            }
        }

        // 清理工作区存储
        const workspaceStorageDir = path.join(this.vscodeUserDataDir, 'User', 'workspaceStorage');
        if (fs.existsSync(workspaceStorageDir)) {
            try {
                const workspaces = fs.readdirSync(workspaceStorageDir);
                workspaces.forEach(workspace => {
                    const augmentWorkspaceDir = path.join(workspaceStorageDir, workspace, this.augmentExtensionId);
                    if (fs.existsSync(augmentWorkspaceDir)) {
                        try {
                            fs.rmSync(augmentWorkspaceDir, { recursive: true, force: true });
                            console.log('✅ 清理工作区存储:', workspace.substring(0, 8) + '...');
                            cleanedItems++;
                        } catch (error) {
                            console.warn('⚠️ 清理工作区存储失败:', error.message);
                        }
                    }
                });
            } catch (error) {
                console.warn('⚠️ 扫描工作区存储失败:', error.message);
            }
        }

        // 清理配置文件中的 Augment 设置
        const settingsFile = path.join(this.vscodeUserDataDir, 'User', 'settings.json');
        if (fs.existsSync(settingsFile)) {
            try {
                const settingsContent = fs.readFileSync(settingsFile, 'utf8');
                const settings = JSON.parse(settingsContent);
                
                const augmentKeys = Object.keys(settings).filter(key => 
                    key.toLowerCase().includes('augment')
                );
                
                if (augmentKeys.length > 0) {
                    augmentKeys.forEach(key => delete settings[key]);
                    fs.writeFileSync(settingsFile, JSON.stringify(settings, null, 2));
                    console.log('✅ 清理配置文件中的 Augment 设置');
                    cleanedItems++;
                }
            } catch (error) {
                console.warn('⚠️ 处理配置文件失败:', error.message);
            }
        }

        return cleanedItems;
    }

    /**
     * 启动 VSCode
     */
    launchVSCode(newMachineId) {
        console.log('\n🚀 启动 VSCode...');
        
        // 设置环境变量
        const env = {
            ...process.env,
            VSCODE_MACHINE_ID: newMachineId,
            MACHINE_ID: newMachineId,
            ELECTRON_MACHINE_ID: newMachineId,
            COMPUTERNAME: `PC-${Math.floor(Math.random() * 99999)}`,
            USERNAME: `User${Math.floor(Math.random() * 9999)}`,
            MAC_ADDRESS: this.generateRandomMac()
        };

        // 启动 VSCode
        const vscode = spawn(this.vscodeExe, [], {
            env: env,
            detached: true,
            stdio: 'ignore'
        });

        vscode.unref();
        console.log('✅ VSCode 已启动');
    }

    /**
     * 生成随机 MAC 地址
     */
    generateRandomMac() {
        const segments = [];
        segments.push('02'); // 本地管理的 MAC 地址前缀
        for (let i = 1; i < 6; i++) {
            segments.push(Math.floor(Math.random() * 256).toString(16).padStart(2, '0'));
        }
        return segments.join(':');
    }

    /**
     * 检查 VSCode 是否正在运行
     */
    async checkVSCodeRunning() {
        return new Promise((resolve) => {
            const { exec } = require('child_process');
            exec('tasklist /FI "IMAGENAME eq Code.exe"', (error, stdout) => {
                if (error) {
                    resolve(false);
                    return;
                }
                resolve(stdout.includes('Code.exe'));
            });
        });
    }

    /**
     * 主执行函数
     */
    async execute() {
        console.log('🔄 Augment 一键重置工具\n');

        // 检查 VSCode 是否在运行
        const isRunning = await this.checkVSCodeRunning();
        if (isRunning) {
            console.log('⚠️  检测到 VSCode 正在运行');
            console.log('💡 为了确保清理完全，建议先关闭 VSCode');
            console.log('');
            console.log('选择操作：');
            console.log('  1. 继续执行（可能有文件被锁定）');
            console.log('  2. 退出，手动关闭 VSCode 后重新运行');
            console.log('');
            
            // 简单起见，我们继续执行
            console.log('🔄 继续执行重置...\n');
        }

        // 1. 更新机器 ID
        console.log('🆔 生成新的设备身份...');
        const newMachineId = this.updateMachineId();

        // 2. 清理 Augment 数据
        console.log('\n🧹 清理 Augment 数据...');
        const cleanedItems = this.cleanAugmentData();

        // 3. 启动 VSCode
        this.launchVSCode(newMachineId);

        console.log('\n🎉 重置完成！\n');
        console.log('📋 接下来：');
        console.log('  1. VSCode 已启动，具有全新的设备身份');
        console.log('  2. 在 Augment 扩展中重新登录');
        console.log('  3. 享受新的试用期！');
        console.log('');
        console.log('💡 下次试用期结束时，再次运行此脚本即可');
        console.log(`📊 本次清理了 ${cleanedItems} 个 Augment 数据项`);

        return true;
    }

    /**
     * 恢复原始机器 ID
     */
    restore() {
        const machineIdFile = path.join(this.vscodeUserDataDir, 'machineid');
        const backupFile = path.join(this.vscodeUserDataDir, 'machineid.original');
        
        if (fs.existsSync(backupFile)) {
            fs.copyFileSync(backupFile, machineIdFile);
            console.log('✅ 原始机器 ID 已恢复');
            return true;
        } else {
            console.log('❌ 未找到原始机器 ID 备份');
            return false;
        }
    }
}

// 主程序
async function main() {
    const args = process.argv.slice(2);
    const action = args[0] || 'reset';
    
    const resetter = new AugmentReset();
    
    try {
        if (action === 'restore') {
            console.log('🔄 恢复原始机器 ID...');
            resetter.restore();
        } else {
            await resetter.execute();
        }
    } catch (error) {
        console.error('❌ 执行失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = AugmentReset;
