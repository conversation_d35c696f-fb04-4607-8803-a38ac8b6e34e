/**
 * 增强版 Augment 认证注入器
 * 支持多种注入方式和更深层的认证集成
 */

const fs = require('fs');
const path = require('path');

class EnhancedAuthInjector {
    constructor() {
        this.configFileName = 'augment-auth-config.json';
        this.extensionId = 'augment.vscode-augment';
    }

    /**
     * 读取认证配置
     */
    loadAuthConfig() {
        try {
            if (!fs.existsSync(this.configFileName)) {
                console.log('❌ 认证配置文件不存在:', this.configFileName);
                return null;
            }

            const configData = fs.readFileSync(this.configFileName, 'utf8');
            const config = JSON.parse(configData);
            
            console.log('✅ 成功读取认证配置');
            return config;
        } catch (error) {
            console.error('❌ 读取认证配置失败:', error.message);
            return null;
        }
    }

    /**
     * 生成增强版注入脚本
     */
    generateEnhancedInjectionScript(authConfig) {
        const authResult = authConfig.authResult;
        
        return `
// Augment 增强版认证注入脚本
(async function() {
    console.log('🔧 正在执行增强版Augment认证注入...');
    
    // 方法1: 直接设置localStorage
    try {
        const authData = {
            accessToken: "${authResult.code}",
            tenantURL: "${authResult.tenant_url}",
            scopes: ["augment:read", "augment:write"],
            timestamp: "${authResult.timestamp}",
            state: "${authResult.state}"
        };
        
        localStorage.setItem('augment-auth-session', JSON.stringify(authData));
        localStorage.setItem('vscode-augment.isLoggedIn', 'true');
        localStorage.setItem('vscode-augment.accessToken', "${authResult.code}");
        localStorage.setItem('vscode-augment.tenantURL', "${authResult.tenant_url}");
        
        console.log('✅ localStorage 认证数据已设置');
    } catch (error) {
        console.warn('⚠️ localStorage 设置失败:', error);
    }
    
    // 方法2: 设置sessionStorage
    try {
        sessionStorage.setItem('augment-login-session', JSON.stringify({
            code: "${authResult.code}",
            tenant_url: "${authResult.tenant_url}",
            state: "${authResult.state}",
            logged_in: true
        }));
        
        console.log('✅ sessionStorage 认证数据已设置');
    } catch (error) {
        console.warn('⚠️ sessionStorage 设置失败:', error);
    }
    
    // 方法3: 模拟扩展API调用
    if (typeof acquireVsCodeApi !== 'undefined') {
        try {
            const vscode = acquireVsCodeApi();
            vscode.postMessage({
                command: 'augment.setAuthData',
                data: {
                    accessToken: "${authResult.code}",
                    tenantURL: "${authResult.tenant_url}",
                    isLoggedIn: true
                }
            });
            console.log('✅ VSCode API 消息已发送');
        } catch (error) {
            console.warn('⚠️ VSCode API 调用失败:', error);
        }
    }
    
    // 方法4: 设置全局变量
    try {
        window.augmentAuthData = {
            accessToken: "${authResult.code}",
            tenantURL: "${authResult.tenant_url}",
            isLoggedIn: true,
            userTier: "professional"
        };
        
        // 设置常见的认证标志
        window.isAugmentLoggedIn = true;
        window.augmentAccessToken = "${authResult.code}";
        
        console.log('✅ 全局变量已设置');
    } catch (error) {
        console.warn('⚠️ 全局变量设置失败:', error);
    }
    
    // 方法5: 尝试直接调用扩展命令
    if (typeof vscode !== 'undefined' && vscode.commands) {
        try {
            // 设置认证上下文
            await vscode.commands.executeCommand('setContext', 'vscode-augment.isLoggedIn', true);
            await vscode.commands.executeCommand('setContext', 'vscode-augment.useOAuth', true);
            await vscode.commands.executeCommand('setContext', 'augment.isAuthenticated', true);
            
            console.log('✅ 扩展上下文已设置');
        } catch (error) {
            console.warn('⚠️ 扩展命令执行失败:', error);
        }
    }
    
    // 方法6: 创建认证事件
    try {
        const authEvent = new CustomEvent('augment-auth-success', {
            detail: {
                accessToken: "${authResult.code}",
                tenantURL: "${authResult.tenant_url}",
                timestamp: new Date().toISOString()
            }
        });
        
        window.dispatchEvent(authEvent);
        document.dispatchEvent(authEvent);
        
        console.log('✅ 认证事件已触发');
    } catch (error) {
        console.warn('⚠️ 事件触发失败:', error);
    }
    
    // 方法7: 设置Cookie（如果可能）
    try {
        document.cookie = \`augment-session=\${encodeURIComponent(JSON.stringify({
            token: "${authResult.code}",
            tenant: "${authResult.tenant_url}",
            logged_in: true
        }))};path=/;max-age=86400\`;
        
        console.log('✅ Cookie 已设置');
    } catch (error) {
        console.warn('⚠️ Cookie 设置失败:', error);
    }
    
    // 等待一下，然后尝试刷新扩展状态
    setTimeout(() => {
        try {
            // 尝试触发扩展重新检查认证状态
            if (typeof vscode !== 'undefined' && vscode.commands) {
                vscode.commands.executeCommand('augment.refreshAuthStatus');
                vscode.commands.executeCommand('workbench.action.reloadWindow');
            }
            
            console.log('🔄 已尝试刷新扩展状态');
        } catch (error) {
            console.warn('⚠️ 刷新状态失败:', error);
        }
    }, 2000);
    
    console.log('🎉 增强版认证注入完成！');
    console.log('💡 如果仍未生效，请尝试：');
    console.log('   1. 重新加载窗口 (Ctrl+R)');
    console.log('   2. 重启VSCode');
    console.log('   3. 禁用并重新启用Augment扩展');
})();
`;
    }

    /**
     * 生成用户数据目录注入文件
     */
    generateUserDataInjection(authConfig, userDataDir) {
        const authResult = authConfig.authResult;
        
        // 创建扩展存储目录
        const extensionStorageDir = path.join(userDataDir, 'User', 'globalStorage', this.extensionId);
        if (!fs.existsSync(extensionStorageDir)) {
            fs.mkdirSync(extensionStorageDir, { recursive: true });
        }
        
        // 创建认证状态文件
        const authStateFile = path.join(extensionStorageDir, 'auth-state.json');
        const authState = {
            isLoggedIn: true,
            accessToken: authResult.code,
            tenantURL: authResult.tenant_url,
            userTier: "professional",
            lastAuthTime: authResult.timestamp,
            authState: authResult.state
        };
        
        fs.writeFileSync(authStateFile, JSON.stringify(authState, null, 2));
        console.log('✅ 用户数据目录认证文件已创建:', authStateFile);
        
        // 创建secrets存储文件
        const secretsFile = path.join(extensionStorageDir, 'secrets.json');
        const secrets = {
            "AugmentLoginSession": JSON.stringify({
                accessToken: authResult.code,
                tenantURL: authResult.tenant_url,
                scopes: ["augment:read", "augment:write"]
            })
        };
        
        fs.writeFileSync(secretsFile, JSON.stringify(secrets, null, 2));
        console.log('✅ Secrets 文件已创建:', secretsFile);
    }

    /**
     * 执行增强版注入
     */
    async executeEnhancedInjection() {
        console.log('🔄 开始执行增强版认证注入...\n');

        // 1. 读取认证配置
        const authConfig = this.loadAuthConfig();
        if (!authConfig) {
            return false;
        }

        console.log('📋 认证配置详情:');
        console.log('  - 认证码:', authConfig.authResult.code ? '***已设置***' : '未设置');
        console.log('  - 状态ID:', authConfig.authResult.state);
        console.log('  - 租户URL:', authConfig.authResult.tenant_url);
        console.log('  - 时间戳:', authConfig.authResult.timestamp);
        console.log();

        // 2. 生成增强版注入脚本
        const enhancedScript = this.generateEnhancedInjectionScript(authConfig);
        const scriptFileName = 'augment-enhanced-injection.js';
        fs.writeFileSync(scriptFileName, enhancedScript);
        console.log('✅ 增强版注入脚本已生成:', scriptFileName);

        // 3. 检查是否存在持久化用户数据目录
        const persistentDir = path.join(__dirname, '..', 'clonevs', 'persistent-vscode-data');
        if (fs.existsSync(persistentDir)) {
            console.log('📁 发现持久化目录，正在注入认证文件...');
            this.generateUserDataInjection(authConfig, persistentDir);
        }

        // 4. 显示使用说明
        this.showEnhancedUsageInstructions();

        return true;
    }

    /**
     * 显示增强版使用说明
     */
    showEnhancedUsageInstructions() {
        console.log('\n📖 增强版使用说明:');
        console.log('');
        console.log('🎯 推荐流程：');
        console.log('  1. 使用 augment-bypass-persistent.bat 启动VSCode');
        console.log('  2. 在VSCode中按F12打开开发者工具');
        console.log('  3. 复制粘贴 augment-enhanced-injection.js 的内容到Console');
        console.log('  4. 等待2秒后按 Ctrl+R 重新加载窗口');
        console.log('');
        console.log('🔧 如果仍未生效：');
        console.log('  1. 重启VSCode（认证文件已保存到用户数据目录）');
        console.log('  2. 在扩展面板中禁用并重新启用Augment扩展');
        console.log('  3. 检查扩展是否需要重新安装');
        console.log('');
        console.log('💡 调试提示：');
        console.log('  - 在Console中查看注入过程的详细日志');
        console.log('  - 检查localStorage和sessionStorage中的认证数据');
        console.log('  - 确认扩展已正确安装并启用');
    }
}

// 命令行使用
async function main() {
    const injector = new EnhancedAuthInjector();
    
    console.log('==========================================');
    console.log('  🚀 Augment 增强版认证注入器');
    console.log('==========================================\n');

    const success = await injector.executeEnhancedInjection();
    
    if (success) {
        console.log('\n🎉 增强版认证注入准备完成！');
    } else {
        console.log('\n❌ 认证注入失败！');
        process.exit(1);
    }
}

if (require.main === module) {
    main().catch(error => {
        console.error('❌ 运行过程中出现错误:', error);
        process.exit(1);
    });
}

module.exports = EnhancedAuthInjector;
