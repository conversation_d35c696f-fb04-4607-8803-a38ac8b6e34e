@echo off
title Auto Injection Launcher

echo Starting VSCode with auto fingerprint injection...

REM Start VSCode in background
start "" "C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe"

REM Wait for VSCode to load
timeout /t 3 /nobreak >nul

REM Inject fingerprint script
echo Injecting fingerprint virtualization...
powershell -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('{F12}'); Start-Sleep -Seconds 2; [System.Windows.Forms.SendKeys]::SendWait('console.log(\"Injecting fingerprint...\");'); [System.Windows.Forms.SendKeys]::SendWait('{ENTER}');"

echo.
echo VSCode started with fingerprint injection
echo Press F12 and paste fingerprint-injection.js content to complete setup
pause
