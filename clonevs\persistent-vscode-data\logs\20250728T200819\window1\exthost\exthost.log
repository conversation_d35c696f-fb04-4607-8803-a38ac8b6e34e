2025-07-28 20:08:24.016 [info] Extension host with pid 18828 started
2025-07-28 20:08:24.094 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-07-28 20:08:24.319 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-07-28 20:08:24.332 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-07-28 20:08:24.418 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-07-28 20:08:24.690 [info] Eager extensions activated
2025-07-28 20:08:24.780 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:08:24.786 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:08:24.827 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:08:25.654 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:08:30.203 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:08:30.668 [info] ExtensionService#_doActivateExtension ms-dotnettools.vscode-dotnet-runtime, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:11:32.402 [info] Extension host terminating: renderer closed the MessagePort
2025-07-28 20:11:32.429 [info] Extension host with pid 18828 exiting with code 0
2025-07-28 20:11:35.387 [info] Extension host with pid 11052 started
2025-07-28 20:11:35.511 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-07-28 20:11:35.559 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onView:augment-chat'
2025-07-28 20:11:36.556 [info] ExtensionService#_doActivateExtension mechatroner.rainbow-csv, startup: false, activationEvent: 'onLanguage:plaintext'
2025-07-28 20:11:36.566 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-07-28 20:11:36.593 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onChatParticipant:github.copilot.editsAgent'
2025-07-28 20:11:37.873 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-07-28 20:11:38.023 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-07-28 20:11:38.479 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-07-28 20:11:39.526 [info] Eager extensions activated
2025-07-28 20:11:39.880 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:11:39.936 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:11:40.026 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:11:44.637 [info] ExtensionService#_doActivateExtension ms-dotnettools.vscode-dotnet-runtime, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:13:26.631 [info] Extension host terminating: renderer closed the MessagePort
2025-07-28 20:13:26.736 [info] Extension host with pid 11052 exiting with code 0
2025-07-28 20:13:28.976 [info] Extension host with pid 20608 started
2025-07-28 20:13:29.004 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-07-28 20:13:29.041 [info] ExtensionService#_doActivateExtension mechatroner.rainbow-csv, startup: false, activationEvent: 'onLanguage:plaintext'
2025-07-28 20:13:29.053 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-07-28 20:13:29.077 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-07-28 20:13:29.169 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-07-28 20:13:29.236 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-07-28 20:13:30.012 [info] Eager extensions activated
2025-07-28 20:13:30.022 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:13:30.041 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:13:30.077 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:13:32.495 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:13:32.935 [info] ExtensionService#_doActivateExtension ms-dotnettools.vscode-dotnet-runtime, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:13:44.724 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:13:47.723 [error] Error: command 'vscode-augment.focusAugmentPanel' not found
    at Lqe.n (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:1243:3892)
    at Lqe.executeCommand (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:1243:3824)
2025-07-28 20:14:20.545 [info] Extension host terminating: renderer closed the MessagePort
2025-07-28 20:14:20.653 [info] Extension host with pid 20608 exiting with code 0
2025-07-28 20:14:27.996 [info] Extension host with pid 27436 started
2025-07-28 20:14:28.329 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-07-28 20:14:28.501 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onView:augment-chat'
2025-07-28 20:14:31.365 [info] ExtensionService#_doActivateExtension mechatroner.rainbow-csv, startup: false, activationEvent: 'onLanguage:plaintext'
2025-07-28 20:14:31.394 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-07-28 20:14:31.637 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onChatParticipant:github.copilot.editsAgent'
2025-07-28 20:14:33.153 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-07-28 20:14:33.332 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-07-28 20:14:33.421 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-07-28 20:14:34.267 [info] Eager extensions activated
2025-07-28 20:14:34.285 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:14:34.306 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:14:34.379 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:14:40.469 [info] ExtensionService#_doActivateExtension ms-dotnettools.vscode-dotnet-runtime, startup: false, activationEvent: 'onStartupFinished'
2025-07-28 20:15:55.750 [info] Extension host terminating: renderer closed the MessagePort
2025-07-28 20:15:55.811 [info] Extension host with pid 27436 exiting with code 0
