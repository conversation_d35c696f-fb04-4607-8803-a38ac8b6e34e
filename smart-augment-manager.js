/**
 * 智能 Augment 管理器
 * 可以保存虚拟身份，选择保持或更换环境
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { spawn } = require('child_process');
const readline = require('readline');

class SmartAugmentManager {
    constructor() {
        this.vscodeUserDataDir = path.join(process.env.APPDATA, 'Code');
        this.vscodeInstallPath = 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code';
        this.vscodeExe = path.join(this.vscodeInstallPath, 'Code.exe');
        this.augmentExtensionId = 'augment.vscode-augment';
        
        // 虚拟身份存储文件
        this.identityFile = path.join(__dirname, 'virtual-identity.json');
        this.originalMachineIdFile = path.join(this.vscodeUserDataDir, 'machineid.original');
    }

    /**
     * 生成新的虚拟身份
     */
    generateVirtualIdentity() {
        return {
            machineId: crypto.randomBytes(32).toString('hex'),
            computername: `PC-${Math.floor(Math.random() * 99999)}`,
            username: `User${Math.floor(Math.random() * 9999)}`,
            macAddress: this.generateRandomMac(),
            sessionId: crypto.randomBytes(16).toString('hex'),
            created: new Date().toISOString(),
            lastUsed: new Date().toISOString()
        };
    }

    /**
     * 生成随机 MAC 地址
     */
    generateRandomMac() {
        const segments = [];
        segments.push('02'); // 本地管理的 MAC 地址前缀
        for (let i = 1; i < 6; i++) {
            segments.push(Math.floor(Math.random() * 256).toString(16).padStart(2, '0'));
        }
        return segments.join(':');
    }

    /**
     * 保存虚拟身份
     */
    saveVirtualIdentity(identity) {
        fs.writeFileSync(this.identityFile, JSON.stringify(identity, null, 2));
        console.log('💾 虚拟身份已保存');
    }

    /**
     * 加载虚拟身份
     */
    loadVirtualIdentity() {
        if (fs.existsSync(this.identityFile)) {
            try {
                const identity = JSON.parse(fs.readFileSync(this.identityFile, 'utf8'));
                return identity;
            } catch (error) {
                console.warn('⚠️ 加载虚拟身份失败:', error.message);
                return null;
            }
        }
        return null;
    }

    /**
     * 显示当前虚拟身份信息
     */
    showCurrentIdentity(identity) {
        console.log('\n📋 当前虚拟身份信息:');
        console.log(`🆔 机器 ID: ${identity.machineId.substring(0, 16)}...`);
        console.log(`💻 计算机名: ${identity.computername}`);
        console.log(`👤 用户名: ${identity.username}`);
        console.log(`🌐 MAC 地址: ${identity.macAddress}`);
        console.log(`📅 创建时间: ${identity.created}`);
        console.log(`🕒 最后使用: ${identity.lastUsed}`);
    }

    /**
     * 应用虚拟身份到系统
     */
    applyVirtualIdentity(identity) {
        // 更新机器 ID 文件
        const machineIdFile = path.join(this.vscodeUserDataDir, 'machineid');
        
        // 备份原始 ID（只备份一次）
        if (fs.existsSync(machineIdFile) && !fs.existsSync(this.originalMachineIdFile)) {
            fs.copyFileSync(machineIdFile, this.originalMachineIdFile);
            console.log('💾 原始机器 ID 已备份');
        }
        
        // 确保目录存在
        if (!fs.existsSync(this.vscodeUserDataDir)) {
            fs.mkdirSync(this.vscodeUserDataDir, { recursive: true });
        }
        
        // 写入虚拟机器 ID
        fs.writeFileSync(machineIdFile, identity.machineId);
        console.log('✅ 虚拟机器 ID 已应用');
        
        // 更新最后使用时间
        identity.lastUsed = new Date().toISOString();
        this.saveVirtualIdentity(identity);
    }

    /**
     * 清理 Augment 数据
     */
    cleanAugmentData() {
        let cleanedItems = 0;

        // 清理全局存储
        const globalStorageDir = path.join(this.vscodeUserDataDir, 'User', 'globalStorage');
        const augmentStorageDir = path.join(globalStorageDir, this.augmentExtensionId);
        
        if (fs.existsSync(augmentStorageDir)) {
            try {
                fs.rmSync(augmentStorageDir, { recursive: true, force: true });
                console.log('✅ 清理 Augment 全局存储');
                cleanedItems++;
            } catch (error) {
                console.warn('⚠️ 清理全局存储失败:', error.message);
            }
        }

        // 清理工作区存储
        const workspaceStorageDir = path.join(this.vscodeUserDataDir, 'User', 'workspaceStorage');
        if (fs.existsSync(workspaceStorageDir)) {
            try {
                const workspaces = fs.readdirSync(workspaceStorageDir);
                workspaces.forEach(workspace => {
                    const augmentWorkspaceDir = path.join(workspaceStorageDir, workspace, this.augmentExtensionId);
                    if (fs.existsSync(augmentWorkspaceDir)) {
                        try {
                            fs.rmSync(augmentWorkspaceDir, { recursive: true, force: true });
                            console.log('✅ 清理工作区存储');
                            cleanedItems++;
                        } catch (error) {
                            console.warn('⚠️ 清理工作区存储失败:', error.message);
                        }
                    }
                });
            } catch (error) {
                console.warn('⚠️ 扫描工作区存储失败:', error.message);
            }
        }

        // 清理配置文件中的 Augment 设置
        const settingsFile = path.join(this.vscodeUserDataDir, 'User', 'settings.json');
        if (fs.existsSync(settingsFile)) {
            try {
                const settingsContent = fs.readFileSync(settingsFile, 'utf8');
                const settings = JSON.parse(settingsContent);
                
                const augmentKeys = Object.keys(settings).filter(key => 
                    key.toLowerCase().includes('augment')
                );
                
                if (augmentKeys.length > 0) {
                    augmentKeys.forEach(key => delete settings[key]);
                    fs.writeFileSync(settingsFile, JSON.stringify(settings, null, 2));
                    console.log('✅ 清理配置文件中的 Augment 设置');
                    cleanedItems++;
                }
            } catch (error) {
                console.warn('⚠️ 处理配置文件失败:', error.message);
            }
        }

        return cleanedItems;
    }

    /**
     * 启动 VSCode 带虚拟身份
     */
    launchVSCodeWithIdentity(identity) {
        console.log('\n🚀 启动 VSCode...');
        
        // 设置环境变量
        const env = {
            ...process.env,
            VSCODE_MACHINE_ID: identity.machineId,
            MACHINE_ID: identity.machineId,
            ELECTRON_MACHINE_ID: identity.machineId,
            COMPUTERNAME: identity.computername,
            USERNAME: identity.username,
            MAC_ADDRESS: identity.macAddress
        };

        // 启动 VSCode
        const vscode = spawn(this.vscodeExe, [], {
            env: env,
            detached: true,
            stdio: 'ignore'
        });

        vscode.unref();
        console.log('✅ VSCode 已启动，使用虚拟身份');
    }

    /**
     * 用户选择菜单
     */
    async showMenu() {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        return new Promise((resolve) => {
            console.log('\n🎯 智能 Augment 管理器');
            console.log('==========================================');
            console.log('1. 保持当前身份（继续使用，无需重新登录）');
            console.log('2. 更换新身份（清理数据，需要重新登录）');
            console.log('3. 查看当前身份信息');
            console.log('4. 恢复原始机器 ID');
            console.log('5. 退出');
            console.log('');

            rl.question('请选择操作 (1-5): ', (answer) => {
                rl.close();
                resolve(answer.trim());
            });
        });
    }

    /**
     * 主执行函数
     */
    async execute() {
        console.log('🎭 智能 Augment 管理器启动\n');

        // 加载现有身份
        let currentIdentity = this.loadVirtualIdentity();
        
        if (currentIdentity) {
            console.log('📋 发现已保存的虚拟身份');
            this.showCurrentIdentity(currentIdentity);
        } else {
            console.log('🆕 未发现已保存的身份，将创建新身份');
        }

        // 显示菜单
        const choice = await this.showMenu();

        switch (choice) {
            case '1': // 保持当前身份
                if (currentIdentity) {
                    console.log('\n🔄 保持当前虚拟身份...');
                    this.applyVirtualIdentity(currentIdentity);
                    this.launchVSCodeWithIdentity(currentIdentity);
                    console.log('\n✅ VSCode 已启动，Augment 登录状态保持不变');
                } else {
                    console.log('\n⚠️ 没有已保存的身份，创建新身份...');
                    await this.createNewIdentity();
                }
                break;

            case '2': // 更换新身份
                console.log('\n🔄 创建新的虚拟身份...');
                await this.createNewIdentity();
                break;

            case '3': // 查看身份信息
                if (currentIdentity) {
                    this.showCurrentIdentity(currentIdentity);
                } else {
                    console.log('\n❌ 没有已保存的虚拟身份');
                }
                break;

            case '4': // 恢复原始 ID
                this.restoreOriginalMachineId();
                break;

            case '5': // 退出
                console.log('\n👋 再见！');
                break;

            default:
                console.log('\n❌ 无效选择');
        }
    }

    /**
     * 创建新身份
     */
    async createNewIdentity() {
        // 生成新身份
        const newIdentity = this.generateVirtualIdentity();
        
        // 清理 Augment 数据
        console.log('🧹 清理 Augment 数据...');
        const cleanedItems = this.cleanAugmentData();
        
        // 应用新身份
        this.applyVirtualIdentity(newIdentity);
        
        // 保存身份
        this.saveVirtualIdentity(newIdentity);
        
        // 启动 VSCode
        this.launchVSCodeWithIdentity(newIdentity);
        
        console.log('\n🎉 新身份创建完成！');
        console.log('📋 接下来需要在 Augment 扩展中重新登录');
        console.log(`📊 清理了 ${cleanedItems} 个 Augment 数据项`);
    }

    /**
     * 恢复原始机器 ID
     */
    restoreOriginalMachineId() {
        const machineIdFile = path.join(this.vscodeUserDataDir, 'machineid');
        
        if (fs.existsSync(this.originalMachineIdFile)) {
            fs.copyFileSync(this.originalMachineIdFile, machineIdFile);
            console.log('✅ 原始机器 ID 已恢复');
            
            // 删除虚拟身份文件
            if (fs.existsSync(this.identityFile)) {
                fs.unlinkSync(this.identityFile);
                console.log('✅ 虚拟身份文件已删除');
            }
        } else {
            console.log('❌ 未找到原始机器 ID 备份');
        }
    }
}

// 主程序
async function main() {
    const manager = new SmartAugmentManager();
    
    try {
        await manager.execute();
    } catch (error) {
        console.error('❌ 执行失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = SmartAugmentManager;
