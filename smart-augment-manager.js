/**
 * 智能 Augment 管理器
 * 可以保存虚拟身份，选择保持或更换环境
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { spawn } = require('child_process');
const readline = require('readline');
const BrowserFingerprintInjector = require('./browser-fingerprint-injector');

class SmartAugmentManager {
    constructor() {
        this.vscodeUserDataDir = path.join(process.env.APPDATA, 'Code');
        this.vscodeInstallPath = 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code';
        this.vscodeExe = path.join(this.vscodeInstallPath, 'Code.exe');
        this.augmentExtensionId = 'augment.vscode-augment';
        
        // 虚拟身份存储文件
        this.identityFile = path.join(__dirname, 'virtual-identity.json');
        this.originalMachineIdFile = path.join(this.vscodeUserDataDir, 'machineid.original');
    }

    /**
     * 生成新的虚拟身份
     */
    generateVirtualIdentity() {
        return {
            machineId: crypto.randomBytes(32).toString('hex'),
            computername: `PC-${Math.floor(Math.random() * 99999)}`,
            username: `User${Math.floor(Math.random() * 9999)}`,
            macAddress: this.generateRandomMac(),
            sessionId: crypto.randomBytes(16).toString('hex'),

            // 浏览器指纹虚拟化
            screen: this.generateVirtualScreen(),
            webgl: this.generateVirtualWebGL(),
            fonts: this.generateVirtualFonts(),
            timezone: this.generateVirtualTimezone(),
            platform: this.generateVirtualPlatform(),

            created: new Date().toISOString(),
            lastUsed: new Date().toISOString()
        };
    }

    /**
     * 生成虚拟屏幕信息
     */
    generateVirtualScreen() {
        const resolutions = [
            { width: 1920, height: 1080 },
            { width: 2560, height: 1440 },
            { width: 1366, height: 768 },
            { width: 1440, height: 900 },
            { width: 1680, height: 1050 },
            { width: 3840, height: 2160 }
        ];

        const resolution = resolutions[Math.floor(Math.random() * resolutions.length)];
        return {
            width: resolution.width,
            height: resolution.height,
            availWidth: resolution.width,
            availHeight: resolution.height - 40, // 减去任务栏高度
            colorDepth: 24,
            pixelDepth: 24
        };
    }

    /**
     * 生成虚拟 WebGL 信息
     */
    generateVirtualWebGL() {
        const renderers = [
            'ANGLE (NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0)',
            'ANGLE (AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0)',
            'ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)',
            'ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)',
            'ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)'
        ];

        return {
            vendor: 'Google Inc. (ANGLE)',
            renderer: renderers[Math.floor(Math.random() * renderers.length)]
        };
    }

    /**
     * 生成虚拟字体列表
     */
    generateVirtualFonts() {
        const baseFonts = [
            'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
            'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
            'Trebuchet MS', 'Arial Black', 'Impact'
        ];

        const additionalFonts = [
            'Microsoft YaHei', 'SimSun', 'SimHei', 'KaiTi', 'FangSong',
            'Calibri', 'Cambria', 'Consolas', 'Segoe UI', 'Tahoma'
        ];

        // 随机选择字体组合
        const selectedFonts = [...baseFonts];
        additionalFonts.forEach(font => {
            if (Math.random() > 0.3) { // 70% 概率包含额外字体
                selectedFonts.push(font);
            }
        });

        return selectedFonts;
    }

    /**
     * 生成虚拟时区
     */
    generateVirtualTimezone() {
        const timezones = [
            'Asia/Shanghai',
            'America/New_York',
            'Europe/London',
            'Asia/Tokyo',
            'Australia/Sydney',
            'America/Los_Angeles',
            'Europe/Paris',
            'Asia/Seoul'
        ];

        return timezones[Math.floor(Math.random() * timezones.length)];
    }

    /**
     * 生成虚拟平台信息
     */
    generateVirtualPlatform() {
        const platforms = [
            {
                platform: 'Win32',
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                oscpu: 'Windows NT 10.0; Win64; x64',
                hardwareConcurrency: Math.floor(Math.random() * 8) + 4 // 4-12 cores
            }
        ];

        return platforms[0]; // 目前只支持 Windows
    }

    /**
     * 生成随机 MAC 地址
     */
    generateRandomMac() {
        const segments = [];
        segments.push('02'); // 本地管理的 MAC 地址前缀
        for (let i = 1; i < 6; i++) {
            segments.push(Math.floor(Math.random() * 256).toString(16).padStart(2, '0'));
        }
        return segments.join(':');
    }

    /**
     * 保存虚拟身份
     */
    saveVirtualIdentity(identity) {
        fs.writeFileSync(this.identityFile, JSON.stringify(identity, null, 2));
        console.log('💾 虚拟身份已保存');
    }

    /**
     * 加载虚拟身份
     */
    loadVirtualIdentity() {
        if (fs.existsSync(this.identityFile)) {
            try {
                const identity = JSON.parse(fs.readFileSync(this.identityFile, 'utf8'));
                return identity;
            } catch (error) {
                console.warn('⚠️ 加载虚拟身份失败:', error.message);
                return null;
            }
        }
        return null;
    }

    /**
     * 显示当前虚拟身份信息
     */
    showCurrentIdentity(identity) {
        console.log('\n📋 当前虚拟身份信息:');
        console.log(`🆔 机器 ID: ${identity.machineId.substring(0, 16)}...`);
        console.log(`💻 计算机名: ${identity.computername}`);
        console.log(`👤 用户名: ${identity.username}`);
        console.log(`🌐 MAC 地址: ${identity.macAddress}`);
        console.log(`📅 创建时间: ${identity.created}`);
        console.log(`🕒 最后使用: ${identity.lastUsed}`);
    }

    /**
     * 应用虚拟身份到系统
     */
    applyVirtualIdentity(identity) {
        // 更新机器 ID 文件
        const machineIdFile = path.join(this.vscodeUserDataDir, 'machineid');
        
        // 备份原始 ID（只备份一次）
        if (fs.existsSync(machineIdFile) && !fs.existsSync(this.originalMachineIdFile)) {
            fs.copyFileSync(machineIdFile, this.originalMachineIdFile);
            console.log('💾 原始机器 ID 已备份');
        }
        
        // 确保目录存在
        if (!fs.existsSync(this.vscodeUserDataDir)) {
            fs.mkdirSync(this.vscodeUserDataDir, { recursive: true });
        }
        
        // 写入虚拟机器 ID
        fs.writeFileSync(machineIdFile, identity.machineId);
        console.log('✅ 虚拟机器 ID 已应用');
        
        // 更新最后使用时间
        identity.lastUsed = new Date().toISOString();
        this.saveVirtualIdentity(identity);
    }

    /**
     * 清理 Augment 数据
     */
    cleanAugmentData() {
        let cleanedItems = 0;

        // 清理全局存储
        const globalStorageDir = path.join(this.vscodeUserDataDir, 'User', 'globalStorage');
        const augmentStorageDir = path.join(globalStorageDir, this.augmentExtensionId);
        
        if (fs.existsSync(augmentStorageDir)) {
            try {
                fs.rmSync(augmentStorageDir, { recursive: true, force: true });
                console.log('✅ 清理 Augment 全局存储');
                cleanedItems++;
            } catch (error) {
                console.warn('⚠️ 清理全局存储失败:', error.message);
            }
        }

        // 清理工作区存储
        const workspaceStorageDir = path.join(this.vscodeUserDataDir, 'User', 'workspaceStorage');
        if (fs.existsSync(workspaceStorageDir)) {
            try {
                const workspaces = fs.readdirSync(workspaceStorageDir);
                workspaces.forEach(workspace => {
                    const augmentWorkspaceDir = path.join(workspaceStorageDir, workspace, this.augmentExtensionId);
                    if (fs.existsSync(augmentWorkspaceDir)) {
                        try {
                            fs.rmSync(augmentWorkspaceDir, { recursive: true, force: true });
                            console.log('✅ 清理工作区存储');
                            cleanedItems++;
                        } catch (error) {
                            console.warn('⚠️ 清理工作区存储失败:', error.message);
                        }
                    }
                });
            } catch (error) {
                console.warn('⚠️ 扫描工作区存储失败:', error.message);
            }
        }

        // 注意：保留 settings.json 中的 Augment 配置
        console.log('💾 保留 settings.json 中的 Augment 配置（按用户要求）');

        // 清理状态数据库中的 Augment 痕迹
        this.cleanStateDatabase();

        // 清理浏览器存储
        this.cleanBrowserStorage();

        // 清理缓存和日志
        this.cleanCacheAndLogs();

        return cleanedItems;
    }

    /**
     * 清理状态数据库中的 Augment 相关数据
     */
    cleanStateDatabase() {
        console.log('💡 状态数据库清理策略：只清理 Augment 相关数据');

        // 对于 SQLite 数据库，我们采用重建策略
        // 因为直接编辑 SQLite 比较复杂，我们备份后删除，让 VSCode 重建
        const userDir = path.join(this.vscodeUserDataDir, 'User');
        const stateFiles = [
            'state.vscdb',
            'state.vscdb-shm',
            'state.vscdb-wal'
        ];

        stateFiles.forEach(file => {
            const filePath = path.join(userDir, file);
            if (fs.existsSync(filePath)) {
                try {
                    // 备份原始文件
                    fs.copyFileSync(filePath, filePath + '.backup');
                    // 删除让 VSCode 重建（这样会清除所有状态，包括 Augment 的）
                    fs.unlinkSync(filePath);
                    console.log('✅ 重置状态数据库:', file);
                } catch (error) {
                    console.warn('⚠️ 重置状态数据库失败:', file, error.message);
                }
            }
        });
    }

    /**
     * 清理浏览器存储中的 Augment 相关数据
     */
    cleanBrowserStorage() {
        const userDir = path.join(this.vscodeUserDataDir, 'User');

        console.log('💡 浏览器存储清理策略：只清理 Augment 相关数据');

        // 对于浏览器存储，我们采用选择性清理
        // 扫描并只删除包含 "augment" 的文件

        const storageDirs = [
            { name: 'IndexedDB', path: path.join(userDir, 'IndexedDB') },
            { name: 'Local Storage', path: path.join(userDir, 'Local Storage') },
            { name: 'Session Storage', path: path.join(userDir, 'Session Storage') }
        ];

        storageDirs.forEach(storage => {
            if (fs.existsSync(storage.path)) {
                try {
                    this.cleanAugmentFromDirectory(storage.path, storage.name);
                } catch (error) {
                    console.warn(`⚠️ 清理 ${storage.name} 失败:`, error.message);
                }
            }
        });
    }

    /**
     * 从目录中清理 Augment 相关文件
     */
    cleanAugmentFromDirectory(dirPath, dirName) {
        try {
            const items = fs.readdirSync(dirPath);
            let cleanedCount = 0;

            items.forEach(item => {
                const itemPath = path.join(dirPath, item);
                const itemLower = item.toLowerCase();

                if (itemLower.includes('augment')) {
                    try {
                        if (fs.statSync(itemPath).isDirectory()) {
                            fs.rmSync(itemPath, { recursive: true, force: true });
                        } else {
                            fs.unlinkSync(itemPath);
                        }
                        console.log(`✅ 清理 ${dirName} 中的 Augment 数据:`, item);
                        cleanedCount++;
                    } catch (error) {
                        console.warn(`⚠️ 清理 ${dirName} 项目失败:`, item, error.message);
                    }
                }
            });

            if (cleanedCount === 0) {
                console.log(`💡 ${dirName} 中未发现 Augment 相关数据`);
            }
        } catch (error) {
            console.warn(`⚠️ 扫描 ${dirName} 失败:`, error.message);
        }
    }

    /**
     * 清理缓存和日志
     */
    cleanCacheAndLogs() {
        // 清理缓存数据
        const cacheDir = path.join(this.vscodeUserDataDir, 'CachedData');
        if (fs.existsSync(cacheDir)) {
            try {
                fs.rmSync(cacheDir, { recursive: true, force: true });
                console.log('✅ 清理缓存数据');
            } catch (error) {
                console.warn('⚠️ 清理缓存失败:', error.message);
            }
        }

        // 清理日志文件
        const logsDir = path.join(this.vscodeUserDataDir, 'logs');
        if (fs.existsSync(logsDir)) {
            try {
                fs.rmSync(logsDir, { recursive: true, force: true });
                console.log('✅ 清理日志文件');
            } catch (error) {
                console.warn('⚠️ 清理日志失败:', error.message);
            }
        }

        // 清理扩展数据库
        const userDir = path.join(this.vscodeUserDataDir, 'User');
        const extensionFiles = [
            'extensions.json',
            'extensionsIdentifiers.json'
        ];

        extensionFiles.forEach(file => {
            const filePath = path.join(userDir, file);
            if (fs.existsSync(filePath)) {
                try {
                    fs.copyFileSync(filePath, filePath + '.backup');
                    fs.unlinkSync(filePath);
                    console.log('✅ 清理扩展数据库:', file);
                } catch (error) {
                    console.warn('⚠️ 清理扩展数据库失败:', file, error.message);
                }
            }
        });
    }

    /**
     * 启动 VSCode 带虚拟身份
     */
    launchVSCodeWithIdentity(identity) {
        console.log('\n🚀 启动 VSCode...');

        // 设置完整的虚拟环境变量
        const env = {
            ...process.env,
            // 机器标识符
            VSCODE_MACHINE_ID: identity.machineId,
            MACHINE_ID: identity.machineId,
            ELECTRON_MACHINE_ID: identity.machineId,
            SYSTEM_UUID: identity.machineId + '-1234-5678-9abc-def012345678',

            // 系统信息
            COMPUTERNAME: identity.computername,
            USERNAME: identity.username,
            USERDOMAIN: 'VIRTUAL-' + Math.floor(Math.random() * 9999),

            // 网络信息
            MAC_ADDRESS: identity.macAddress,
            ETHERNET_MAC: identity.macAddress,
            WIFI_MAC: identity.macAddress,

            // 硬件信息
            PROCESSOR_IDENTIFIER: 'Virtual CPU ' + Math.floor(Math.random() * 9999),
            PROCESSOR_REVISION: Math.floor(Math.random() * 9999).toString(16),
            NUMBER_OF_PROCESSORS: (Math.floor(Math.random() * 8) + 4).toString(),

            // Chrome/Electron 标识符
            CHROME_MACHINE_ID: identity.machineId,
            ELECTRON_USER_DATA_DIR: path.join(process.env.TEMP, 'vscode-virtual-' + Math.floor(Math.random() * 9999)),

            // 安全设置
            ELECTRON_NO_SECURITY_WARN: '1',
            ELECTRON_DISABLE_SECURITY_WARNINGS: '1',
            ELECTRON_NO_SANDBOX: '1'
        };

        console.log('🎭 设置虚拟环境变量:');
        console.log(`  🆔 机器ID: ${identity.machineId.substring(0, 16)}...`);
        console.log(`  💻 计算机名: ${identity.computername}`);
        console.log(`  👤 用户名: ${identity.username}`);
        console.log(`  🌐 MAC地址: ${identity.macAddress}`);

        // 启动 VSCode
        const vscode = spawn(this.vscodeExe, [], {
            env: env,
            detached: true,
            stdio: 'ignore'
        });

        vscode.unref();
        console.log('✅ VSCode 已启动，使用完整虚拟身份');

        // 生成浏览器指纹注入脚本
        this.generateBrowserFingerprintScript(identity);
    }

    /**
     * 生成浏览器指纹注入脚本
     */
    generateBrowserFingerprintScript(identity) {
        try {
            const scriptPath = path.join(__dirname, 'browser-fingerprint-injection.js');
            BrowserFingerprintInjector.saveInjectionScript(identity, scriptPath);

            console.log('\n🎭 浏览器指纹虚拟化脚本已生成');
            console.log('📁 脚本位置:', scriptPath);
            console.log('💡 使用方法:');
            console.log('  1. 在 VSCode 中按 F12 打开开发者工具');
            console.log('  2. 切换到 Console 标签');
            console.log('  3. 复制粘贴脚本内容并执行');
            console.log('  4. 浏览器指纹将被完全虚拟化');

        } catch (error) {
            console.warn('⚠️ 生成浏览器指纹脚本失败:', error.message);
        }
    }

    /**
     * 用户选择菜单
     */
    async showMenu() {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        return new Promise((resolve) => {
            console.log('\n🎯 智能 Augment 管理器');
            console.log('==========================================');
            console.log('1. 保持当前身份（继续使用，无需重新登录）');
            console.log('2. 更换新身份（清理数据，需要重新登录）');
            console.log('3. 查看当前身份信息');
            console.log('4. 恢复原始机器 ID');
            console.log('5. 退出');
            console.log('');

            rl.question('请选择操作 (1-5): ', (answer) => {
                rl.close();
                resolve(answer.trim());
            });
        });
    }

    /**
     * 主执行函数
     */
    async execute() {
        console.log('🎭 智能 Augment 管理器启动\n');

        // 加载现有身份
        let currentIdentity = this.loadVirtualIdentity();
        
        if (currentIdentity) {
            console.log('📋 发现已保存的虚拟身份');
            this.showCurrentIdentity(currentIdentity);
        } else {
            console.log('🆕 未发现已保存的身份，将创建新身份');
        }

        // 显示菜单
        const choice = await this.showMenu();

        switch (choice) {
            case '1': // 保持当前身份
                if (currentIdentity) {
                    console.log('\n🔄 保持当前虚拟身份...');
                    this.applyVirtualIdentity(currentIdentity);
                    this.launchVSCodeWithIdentity(currentIdentity);
                    console.log('\n✅ VSCode 已启动，Augment 登录状态保持不变');
                } else {
                    console.log('\n⚠️ 没有已保存的身份，创建新身份...');
                    await this.createNewIdentity();
                }
                break;

            case '2': // 更换新身份
                console.log('\n🔄 创建新的虚拟身份...');
                await this.createNewIdentity();
                break;

            case '3': // 查看身份信息
                if (currentIdentity) {
                    this.showCurrentIdentity(currentIdentity);
                } else {
                    console.log('\n❌ 没有已保存的虚拟身份');
                }
                break;

            case '4': // 恢复原始 ID
                this.restoreOriginalMachineId();
                break;

            case '5': // 退出
                console.log('\n👋 再见！');
                break;

            default:
                console.log('\n❌ 无效选择');
        }
    }

    /**
     * 创建新身份
     */
    async createNewIdentity() {
        // 生成新身份
        const newIdentity = this.generateVirtualIdentity();
        
        // 清理 Augment 数据
        console.log('🧹 清理 Augment 数据...');
        const cleanedItems = this.cleanAugmentData();
        
        // 应用新身份
        this.applyVirtualIdentity(newIdentity);
        
        // 保存身份
        this.saveVirtualIdentity(newIdentity);
        
        // 启动 VSCode
        this.launchVSCodeWithIdentity(newIdentity);
        
        console.log('\n🎉 新身份创建完成！');
        console.log('📋 接下来需要在 Augment 扩展中重新登录');
        console.log(`📊 清理了 ${cleanedItems} 个 Augment 数据项`);
    }

    /**
     * 恢复原始机器 ID
     */
    restoreOriginalMachineId() {
        const machineIdFile = path.join(this.vscodeUserDataDir, 'machineid');
        
        if (fs.existsSync(this.originalMachineIdFile)) {
            fs.copyFileSync(this.originalMachineIdFile, machineIdFile);
            console.log('✅ 原始机器 ID 已恢复');
            
            // 删除虚拟身份文件
            if (fs.existsSync(this.identityFile)) {
                fs.unlinkSync(this.identityFile);
                console.log('✅ 虚拟身份文件已删除');
            }
        } else {
            console.log('❌ 未找到原始机器 ID 备份');
        }
    }
}

// 主程序
async function main() {
    const manager = new SmartAugmentManager();
    
    try {
        await manager.execute();
    } catch (error) {
        console.error('❌ 执行失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = SmartAugmentManager;
