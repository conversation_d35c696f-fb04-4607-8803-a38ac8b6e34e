# Augment VSCode 认证处理工具

这是一个用于在虚拟VSCode环境中处理Augment认证回调的工具集。当您在虚拟环境中使用Augment扩展时，认证回调URL会自动打开系统默认的VSCode，本工具可以帮您截取和处理这些认证信息。

## 🎯 解决的问题

当使用Augment进行登录时，系统会生成类似这样的认证URL：
```
vscode://augment.vscode-augment/auth/result?code=_f103ee5c71fadbc084c619a47fa3ac88&state=75b66fdb-5bfc-4f39-b60e-b675283947f7&tenant_url=https%3A%2F%2Fd17.api.augmentcode.com%2F
```

这个URL会自动打开系统默认的VSCode，而不是您想要使用的虚拟VSCode环境。

## 📦 工具包含

### 🔧 认证处理工具
1. **`auth-handler.js`** - 命令行认证URL处理工具
2. **`auth-interceptor.js`** - Web界面认证拦截服务器
3. **`handle-auth.bat`** - Windows批处理脚本
4. **`start-interceptor.bat`** - 拦截服务器启动脚本

### 💉 虚拟VSCode注入工具
5. **`virtual-vscode-auth-injector.js`** - 虚拟VSCode认证注入器
6. **`inject-auth.bat`** - 注入器启动脚本

## 🚀 快速开始

### 方法一：使用Web拦截器（推荐）

1. **启动拦截服务器**
   ```bash
   # 双击运行或命令行执行
   start-interceptor.bat
   ```

2. **打开Web界面**
   - 在浏览器中访问 `http://localhost:3000`
   - 您会看到一个友好的Web界面

3. **处理认证**
   - 当Augment要求登录时，**不要**让系统自动处理认证URL
   - 复制完整的认证URL
   - 粘贴到Web界面的输入框中
   - 点击"处理认证"按钮

4. **获取结果**
   - 认证信息会自动解析并显示
   - 配置文件会保存为 `augment-auth-config.json`

### 方法二：使用命令行工具

1. **直接处理认证URL**
   ```bash
   # 使用批处理脚本
   handle-auth.bat "vscode://augment.vscode-augment/auth/result?code=...&state=...&tenant_url=..."
   
   # 或直接使用Node.js
   node auth-handler.js "vscode://augment.vscode-augment/auth/result?code=...&state=...&tenant_url=..."
   ```

2. **查看结果**
   - 认证信息会显示在控制台
   - 配置文件会保存为 `augment-auth-config.json`

## 🎯 在虚拟VSCode中使用

完成认证处理后，您需要将认证信息注入到虚拟VSCode环境中：

### 第一步：生成注入脚本

```bash
# 使用批处理脚本（推荐）
inject-auth.bat

# 或直接使用Node.js
node virtual-vscode-auth-injector.js
```

### 第二步：在虚拟VSCode中注入

1. **打开开发者工具** - 在虚拟VSCode中按 `F12`
2. **切换到Console** - 点击 Console 标签
3. **执行注入脚本** - 复制 `augment-auth-injection.js` 的内容并粘贴到控制台执行
4. **验证注入结果** - 查看控制台输出确认注入成功

### 第三步：验证认证状态

注入完成后，您应该能够看到：
- ✅ Augment扩展显示已登录状态
- ✅ 可以使用Chat、Completions等功能
- ✅ 扩展命令和功能正常可用

### 注入方式选择

**方法一：开发者工具注入（推荐）**
- 适用于大多数虚拟VSCode环境
- 立即生效，无需重启
- 可以看到详细的注入过程

**方法二：扩展设置**
- 在设置中手动配置
- 适用于有设置界面的环境
- 使用 `augment-vscode-config.json` 中的配置

**方法三：环境变量**
- 设置系统环境变量
- 需要重启VSCode生效
- 适用于容器化环境

## 📋 系统要求

- **Node.js**: 版本12.0或更高
- **操作系统**: Windows (批处理脚本)，也支持macOS/Linux (直接使用Node.js)

## 📄 输出文件

处理完成后，会生成 `augment-auth-config.json` 文件，包含以下结构：

```json
{
  "extensionId": "augment.vscode-augment",
  "authResult": {
    "code": "认证代码",
    "state": "状态标识",
    "tenant_url": "租户URL",
    "timestamp": "处理时间戳"
  }
}
```

## 🔧 高级使用

### 自定义端口

```bash
# 启动在指定端口
node auth-interceptor.js 8080
```

### 编程式使用

```javascript
const AugmentAuthHandler = require('./auth-handler');

const handler = new AugmentAuthHandler();
const authUrl = "vscode://augment.vscode-augment/auth/result?...";

handler.handleAuthCallback(authUrl).then(result => {
    if (result.success) {
        console.log('认证成功:', result.data);
    } else {
        console.error('认证失败:', result.error);
    }
});
```

## 🛠️ 故障排除

### 常见问题

1. **找不到Node.js**
   - 下载安装：https://nodejs.org/
   - 确保安装后重启命令行

2. **端口已被占用**
   - 尝试使用不同端口：`node auth-interceptor.js 8080`
   - 或关闭占用端口的程序

3. **认证URL格式错误**
   - 确保URL以 `vscode://augment.vscode-augment/` 开头
   - 检查URL是否完整，包含所有参数

4. **无法保存配置文件**
   - 检查目录写入权限
   - 确保磁盘空间充足

### 调试模式

启动时添加调试标志：
```bash
node auth-interceptor.js 3000 --debug
```

## 🔐 安全说明

- 认证信息仅在本地处理，不会发送到外部服务器
- 生成的配置文件包含敏感信息，请妥善保管
- 建议在使用后及时删除临时认证文件

## 📈 完整工作流程

```mermaid
graph TD
    A[用户尝试登录Augment] --> B[获得认证回调URL<br/>vscode://augment.vscode-augment/auth/result?...]
    B --> C{选择处理方式}
    C -->|Web界面| D[启动拦截服务器<br/>start-interceptor.bat]
    C -->|命令行| E[使用批处理脚本<br/>handle-auth.bat]
    D --> F[在浏览器中粘贴URL]
    E --> G[命令行输入URL]
    F --> H[解析认证参数]
    G --> H
    H --> I[生成配置文件<br/>augment-auth-config.json]
    I --> J[运行注入器<br/>inject-auth.bat]
    J --> K[生成注入脚本<br/>augment-auth-injection.js]
    K --> L[在虚拟VSCode中打开F12]
    L --> M[复制粘贴注入脚本到Console]
    M --> N[验证Augment扩展已登录]
    N --> O[开始使用Augment功能]
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📜 许可证

MIT License - 详见LICENSE文件

---

## 📚 文档资源

- **[README.md](README.md)** - 项目主文档和快速入门指南
- **[USAGE_DEMO.md](USAGE_DEMO.md)** - 详细的使用演示和故障排除
- **[FILE_LIST.md](FILE_LIST.md)** - 完整的项目文件清单和说明

## 💬 支持与帮助

**💡 提示**: 如果您在使用过程中遇到任何问题：

1. 📖 查看 [详细使用演示](USAGE_DEMO.md) 了解完整流程
2. 🔧 参考故障排除部分寻找解决方案  
3. 💬 创建Issue描述您的问题和环境信息
4. 📧 联系技术支持获取专业帮助 