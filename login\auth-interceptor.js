/**
 * Augment 认证拦截服务器
 * 用于截取认证回调并防止自动打开默认VSCode
 */

const http = require('http');
const url = require('url');
const querystring = require('querystring');
const fs = require('fs');
const path = require('path');

class AuthInterceptor {
    constructor(port = 3000) {
        this.port = port;
        this.server = null;
        this.authResults = [];
    }

    /**
     * 启动拦截服务器
     */
    start() {
        this.server = http.createServer((req, res) => {
            this.handleRequest(req, res);
        });

        this.server.listen(this.port, () => {
            console.log(`🚀 认证拦截服务器已启动`);
            console.log(`📍 服务地址: http://localhost:${this.port}`);
            console.log(`💡 请将此地址配置为认证回调URL的替代方案`);
            console.log(`⏹️  按 Ctrl+C 停止服务器\n`);
        });

        // 处理优雅停止
        process.on('SIGINT', () => {
            console.log('\n🛑 正在停止服务器...');
            this.server.close(() => {
                console.log('✅ 服务器已停止');
                process.exit(0);
            });
        });
    }

    /**
     * 处理HTTP请求
     */
    handleRequest(req, res) {
        const parsedUrl = url.parse(req.url, true);
        const pathname = parsedUrl.pathname;

        console.log(`📥 收到请求: ${req.method} ${req.url}`);

        // 设置CORS头
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

        if (req.method === 'OPTIONS') {
            res.writeHead(200);
            res.end();
            return;
        }

        switch (pathname) {
            case '/':
                this.serveDashboard(req, res);
                break;
            case '/auth/callback':
                this.handleAuthCallback(req, res, parsedUrl.query);
                break;
            case '/auth/manual':
                this.handleManualAuth(req, res);
                break;
            case '/api/results':
                this.serveAuthResults(req, res);
                break;
            default:
                this.serve404(req, res);
        }
    }

    /**
     * 服务主页面
     */
    serveDashboard(req, res) {
        const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augment 认证拦截器</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
        .section { margin: 20px 0; padding: 20px; background: #f9f9f9; border-radius: 5px; }
        .url-input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: monospace; }
        .btn { background: #007acc; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #005a9e; }
        .result { background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #ffe8e8; border: 1px solid #f44336; }
        .code { background: #f0f0f0; padding: 10px; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
        .status { padding: 5px 10px; border-radius: 3px; margin: 5px 0; display: inline-block; }
        .status.success { background: #4caf50; color: white; }
        .status.waiting { background: #ff9800; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Augment 认证拦截器</h1>
        
        <div class="section">
            <h3>📋 使用说明</h3>
            <p>1. 当Augment要求登录时，会生成一个类似这样的认证URL：</p>
            <div class="code">vscode://augment.vscode-augment/auth/result?code=...&state=...&tenant_url=...</div>
            <p>2. 不要让系统自动处理该URL，而是复制URL并粘贴到下面的输入框中</p>
            <p>3. 点击"处理认证"按钮来提取认证信息</p>
        </div>

        <div class="section">
            <h3>🔗 手动输入认证URL</h3>
            <textarea id="authUrl" class="url-input" rows="3" placeholder="粘贴您的认证URL..."></textarea>
            <br><br>
            <button class="btn" onclick="processAuth()">🔄 处理认证</button>
            <button class="btn" onclick="clearResults()">🗑️ 清空结果</button>
        </div>

        <div class="section">
            <h3>📊 服务状态</h3>
            <div class="status waiting">⏳ 等待认证回调</div>
            <p>拦截服务器地址: <code>http://localhost:${this.port}/auth/callback</code></p>
        </div>

        <div id="results" class="section" style="display: none;">
            <h3>✅ 认证结果</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        async function processAuth() {
            const authUrl = document.getElementById('authUrl').value.trim();
            if (!authUrl) {
                alert('请输入认证URL');
                return;
            }

            try {
                const response = await fetch('/auth/manual', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ authUrl: authUrl })
                });

                const result = await response.json();
                showResult(result);
            } catch (error) {
                alert('处理失败: ' + error.message);
            }
        }

        function showResult(result) {
            const resultsDiv = document.getElementById('results');
            const contentDiv = document.getElementById('resultContent');
            
            if (result.success) {
                contentDiv.innerHTML = '<div class="result">' +
                    '<h4>🎉 认证数据解析成功</h4>' +
                    '<p><strong>认证码:</strong> <code>' + (result.data.code || 'N/A') + '</code></p>' +
                    '<p><strong>状态:</strong> <code>' + (result.data.state || 'N/A') + '</code></p>' +
                    '<p><strong>租户URL:</strong> <code>' + (result.data.tenant_url || 'N/A') + '</code></p>' +
                    '<h4>📄 JSON配置</h4>' +
                    '<div class="code">' + JSON.stringify(result.config, null, 2) + '</div>' +
                    '</div>';
            } else {
                contentDiv.innerHTML = '<div class="result error">' +
                    '<h4>❌ 处理失败</h4>' +
                    '<p>' + result.error + '</p>' +
                    '</div>';
            }
            
            resultsDiv.style.display = 'block';
        }

        function clearResults() {
            document.getElementById('authUrl').value = '';
            document.getElementById('results').style.display = 'none';
        }

        // 定期检查新的认证结果
        setInterval(async () => {
            try {
                const response = await fetch('/api/results');
                const results = await response.json();
                if (results.length > 0) {
                    const latest = results[results.length - 1];
                    if (latest && !latest.displayed) {
                        showResult(latest);
                        latest.displayed = true;
                    }
                }
            } catch (error) {
                // 忽略轮询错误
            }
        }, 2000);
    </script>
</body>
</html>`;

        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(html);
    }

    /**
     * 处理认证回调
     */
    handleAuthCallback(req, res, query) {
        console.log('🔓 收到认证回调:', query);

        const authData = {
            code: query.code,
            state: query.state,
            tenant_url: query.tenant_url,
            timestamp: new Date().toISOString()
        };

        const result = {
            success: true,
            data: authData,
            config: {
                extensionId: 'augment.vscode-augment',
                authResult: authData
            },
            message: '认证回调处理成功'
        };

        this.authResults.push(result);
        this.saveAuthConfig(result.config);

        // 返回成功页面
        const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>认证成功</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .success { color: #4caf50; font-size: 24px; }
    </style>
</head>
<body>
    <h1 class="success">✅ 认证成功!</h1>
    <p>认证信息已保存，您可以关闭此页面。</p>
    <script>setTimeout(() => window.close(), 3000);</script>
</body>
</html>`;

        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(html);
    }

    /**
     * 处理手动认证
     */
    handleManualAuth(req, res) {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });

        req.on('end', () => {
            try {
                const { authUrl } = JSON.parse(body);
                const parsedUrl = url.parse(authUrl);
                const query = querystring.parse(parsedUrl.query);

                if (parsedUrl.protocol !== 'vscode:' || !parsedUrl.hostname.includes('augment')) {
                    throw new Error('无效的认证URL格式');
                }

                const authData = {
                    code: query.code,
                    state: query.state,
                    tenant_url: query.tenant_url,
                    timestamp: new Date().toISOString()
                };

                const result = {
                    success: true,
                    data: authData,
                    config: {
                        extensionId: 'augment.vscode-augment',
                        authResult: authData
                    },
                    message: '认证URL处理成功'
                };

                this.authResults.push(result);
                this.saveAuthConfig(result.config);

                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify(result));

                console.log('✅ 手动认证处理成功');
            } catch (error) {
                const errorResult = {
                    success: false,
                    error: error.message
                };

                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify(errorResult));

                console.log('❌ 手动认证处理失败:', error.message);
            }
        });
    }

    /**
     * 返回认证结果
     */
    serveAuthResults(req, res) {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(this.authResults));
    }

    /**
     * 404页面
     */
    serve404(req, res) {
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('404 Not Found');
    }

    /**
     * 保存认证配置到文件
     */
    saveAuthConfig(config) {
        try {
            const configPath = path.join(process.cwd(), 'augment-auth-config.json');
            fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
            console.log('📁 认证配置已保存到:', configPath);
        } catch (error) {
            console.error('❌ 保存配置失败:', error.message);
        }
    }
}

// 启动服务器
if (require.main === module) {
    const port = process.argv[2] || 3000;
    const interceptor = new AuthInterceptor(port);
    interceptor.start();
}

module.exports = AuthInterceptor; 