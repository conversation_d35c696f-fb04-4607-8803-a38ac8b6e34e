
// Augment认证注入脚本 - 自动生成
(function() {
    console.log('🔧 正在注入Augment认证信息...');
    
    // 模拟VSCode的secrets API
    if (typeof vscode !== 'undefined' && vscode.context && vscode.context.secrets) {
        const secrets = {
  "AugmentLoginSession": "{\"accessToken\":\"_f103ee5c71fadbc084c619a47fa3ac88\",\"tenantURL\":\"https://d17.api.augmentcode.com/\",\"scopes\":[\"augment:read\",\"augment:write\"]}"
};
        
        // 注入secrets
        for (const [key, value] of Object.entries(secrets)) {
            try {
                // 模拟异步存储
                if (vscode.context.secrets.store) {
                    vscode.context.secrets.store(key, value);
                }
                console.log('✅ 已注入Secret:', key);
            } catch (error) {
                console.warn('⚠️ 注入Secret失败:', key, error);
            }
        }
    }

    // 模拟VSCode的globalState API
    if (typeof vscode !== 'undefined' && vscode.context && vscode.context.globalState) {
        const globalState = {
  "vscode-augment.isLoggedIn": true,
  "hasTrackedInstall": true,
  "lastEnabledExtensionVersion": "0.516.0"
};
        
        // 注入全局状态
        for (const [key, value] of Object.entries(globalState)) {
            try {
                if (vscode.context.globalState.update) {
                    vscode.context.globalState.update(key, value);
                }
                console.log('✅ 已注入GlobalState:', key);
            } catch (error) {
                console.warn('⚠️ 注入GlobalState失败:', key, error);
            }
        }
    }

    // 模拟VSCode的workspaceState API
    if (typeof vscode !== 'undefined' && vscode.context && vscode.context.workspaceState) {
        const workspaceState = {
  "vscode-augment.isLoggedIn": true,
  "vscode-augment.userTier": "professional",
  "vscode-augment.lastAuthTime": "2025-07-28T11:53:32.794Z",
  "vscode-augment.authState": "75b66fdb-5bfc-4f39-b60e-b675283947f7"
};
        
        // 注入工作区状态
        for (const [key, value] of Object.entries(workspaceState)) {
            try {
                if (vscode.context.workspaceState.update) {
                    vscode.context.workspaceState.update(key, value);
                }
                console.log('✅ 已注入WorkspaceState:', key);
            } catch (error) {
                console.warn('⚠️ 注入WorkspaceState失败:', key, error);
            }
        }
    }

    // 设置上下文变量
    if (typeof vscode !== 'undefined' && vscode.commands) {
        try {
            vscode.commands.executeCommand('setContext', 'vscode-augment.isLoggedIn', true);
            vscode.commands.executeCommand('setContext', 'vscode-augment.useOAuth', true);
            console.log('✅ 已设置认证上下文');
        } catch (error) {
            console.warn('⚠️ 设置上下文失败:', error);
        }
    }

    console.log('🎉 Augment认证信息注入完成！');
})();
