{"workbench.secondarySideBar.defaultVisibility": "visibleInWorkspace", "chat.editRequests": "hover", "chat.edits2.enabled": false, "editor.tokenColorCustomizations": {"[*Light*]": {"textMateRules": [{"scope": "ref.matchtext", "settings": {"foreground": "#000"}}]}, "[*Dark*]": {"textMateRules": [{"scope": "ref.matchtext", "settings": {"foreground": "#fff"}}]}}, "workbench.editorAssociations": {"*.copilotmd": "vscode.markdown.preview.editor"}, "C_Cpp.debugShortcut": false, "python.locator": "native"}