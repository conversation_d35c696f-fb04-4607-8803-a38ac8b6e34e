@echo off
title Augment Bypass - Main VSCode with Virtual Fingerprint

echo ==========================================
echo   Augment Bypass - Main VSCode Version
echo ==========================================
echo.
echo 🎯 Using real VSCode with virtual fingerprint
echo 🔗 Auth URLs will work directly
echo 🎭 Hardware fingerprint virtualized
echo.

REM Generate session fingerprint
set SESSION_ID=%RANDOM%%TIME:~6,2%%DATE:~6,4%
echo 🎲 Session ID: %SESSION_ID%

REM Set virtual environment variables
set VSCODE_MACHINE_ID=181b728e5a9681be39986d481163c642cd4a26dfae45ab113130abf4a26def90
set MACHINE_ID=181b728e5a9681be39986d481163c642cd4a26dfae45ab113130abf4a26def90
set ELECTRON_MACHINE_ID=181b728e5a9681be39986d481163c642cd4a26dfae45ab113130abf4a26def90
set MAC_ADDRESS=ba:55:57:aa:d4:7d
set COMPUTERNAME=PC-%RANDOM%
set USERNAME=VirtualUser%RANDOM%

REM Security settings
set ELECTRON_NO_SECURITY_WARN=1
set ELECTRON_DISABLE_SECURITY_WARNINGS=1

echo 🚀 Starting main VSCode with virtual fingerprint...
echo.

REM Start VSCode with fingerprint injection
"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe" ^
  --enable-logging ^
  --log-level=info ^
  %*

echo.
echo ✅ VSCode session ended
echo 💡 Tips:
echo    1. Auth URLs work directly in this mode
echo    2. Augment sees this as a new device
echo    3. All your extensions and settings are preserved
echo.
pause
