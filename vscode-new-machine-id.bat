@echo off
title VSCode with New Machine ID

echo ==========================================
echo   VSCode with Completely New Machine ID
echo ==========================================
echo.
echo 🆔 New Machine ID: 500cc2405bfcba51...
echo 🔄 All caches cleared
echo 🧹 Extension storage reset
echo.

REM Set environment variables for extra protection
set VSCODE_MACHINE_ID=500cc2405bfcba516a8d7e36050fcca7c3682c3cf98b601ed074978b70021255
set MACHINE_ID=500cc2405bfcba516a8d7e36050fcca7c3682c3cf98b601ed074978b70021255
set ELECTRON_MACHINE_ID=500cc2405bfcba516a8d7e36050fcca7c3682c3cf98b601ed074978b70021255

REM Generate random system info
set /a RANDOM_NUM=%RANDOM% * 32768 + %RANDOM%
set COMPUTERNAME=PC-%RANDOM_NUM%
set USERNAME=User%RANDOM%
set MAC_ADDRESS=02:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%

echo 🚀 Starting VSCode with new identity...
echo.

"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe" %*

echo.
echo ✅ VSCode session ended
echo 💡 Machine ID has been permanently changed
echo 🔄 Augment should see this as a completely new device
echo.
pause
