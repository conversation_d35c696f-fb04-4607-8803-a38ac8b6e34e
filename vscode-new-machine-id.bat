@echo off
title VSCode with New Machine ID

echo ==========================================
echo   VSCode with Completely New Machine ID
echo ==========================================
echo.
echo 🆔 New Machine ID: cedb41fab5ec8992...
echo 🔄 All caches cleared
echo 🧹 Extension storage reset
echo.

REM Set environment variables for extra protection
set VSCODE_MACHINE_ID=cedb41fab5ec89925be5129ecab2b8f55c504fec7845a17e60e44ba770e3fb30
set MACHINE_ID=cedb41fab5ec89925be5129ecab2b8f55c504fec7845a17e60e44ba770e3fb30
set ELECTRON_MACHINE_ID=cedb41fab5ec89925be5129ecab2b8f55c504fec7845a17e60e44ba770e3fb30

REM Generate random system info
set /a RANDOM_NUM=%RANDOM% * 32768 + %RANDOM%
set COMPUTERNAME=PC-%RANDOM_NUM%
set USERNAME=User%RANDOM%
set MAC_ADDRESS=02:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%

echo 🚀 Starting VSCode with new identity...
echo.

"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe" %*

echo.
echo ✅ VSCode session ended
echo 💡 Machine ID has been permanently changed
echo 🔄 Augment should see this as a completely new device
echo.
pause
