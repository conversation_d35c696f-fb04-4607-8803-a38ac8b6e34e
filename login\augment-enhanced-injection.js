
// Augment 增强版认证注入脚本
(async function() {
    console.log('🔧 正在执行增强版Augment认证注入...');
    
    // 方法1: 直接设置localStorage
    try {
        const authData = {
            accessToken: "_f103ee5c71fadbc084c619a47fa3ac88",
            tenantURL: "https://d17.api.augmentcode.com/",
            scopes: ["augment:read", "augment:write"],
            timestamp: "2025-07-28T11:49:02.343Z",
            state: "75b66fdb-5bfc-4f39-b60e-b675283947f7"
        };
        
        localStorage.setItem('augment-auth-session', JSON.stringify(authData));
        localStorage.setItem('vscode-augment.isLoggedIn', 'true');
        localStorage.setItem('vscode-augment.accessToken', "_f103ee5c71fadbc084c619a47fa3ac88");
        localStorage.setItem('vscode-augment.tenantURL', "https://d17.api.augmentcode.com/");
        
        console.log('✅ localStorage 认证数据已设置');
    } catch (error) {
        console.warn('⚠️ localStorage 设置失败:', error);
    }
    
    // 方法2: 设置sessionStorage
    try {
        sessionStorage.setItem('augment-login-session', JSON.stringify({
            code: "_f103ee5c71fadbc084c619a47fa3ac88",
            tenant_url: "https://d17.api.augmentcode.com/",
            state: "75b66fdb-5bfc-4f39-b60e-b675283947f7",
            logged_in: true
        }));
        
        console.log('✅ sessionStorage 认证数据已设置');
    } catch (error) {
        console.warn('⚠️ sessionStorage 设置失败:', error);
    }
    
    // 方法3: 模拟扩展API调用
    if (typeof acquireVsCodeApi !== 'undefined') {
        try {
            const vscode = acquireVsCodeApi();
            vscode.postMessage({
                command: 'augment.setAuthData',
                data: {
                    accessToken: "_f103ee5c71fadbc084c619a47fa3ac88",
                    tenantURL: "https://d17.api.augmentcode.com/",
                    isLoggedIn: true
                }
            });
            console.log('✅ VSCode API 消息已发送');
        } catch (error) {
            console.warn('⚠️ VSCode API 调用失败:', error);
        }
    }
    
    // 方法4: 设置全局变量
    try {
        window.augmentAuthData = {
            accessToken: "_f103ee5c71fadbc084c619a47fa3ac88",
            tenantURL: "https://d17.api.augmentcode.com/",
            isLoggedIn: true,
            userTier: "professional"
        };
        
        // 设置常见的认证标志
        window.isAugmentLoggedIn = true;
        window.augmentAccessToken = "_f103ee5c71fadbc084c619a47fa3ac88";
        
        console.log('✅ 全局变量已设置');
    } catch (error) {
        console.warn('⚠️ 全局变量设置失败:', error);
    }
    
    // 方法5: 尝试直接调用扩展命令
    if (typeof vscode !== 'undefined' && vscode.commands) {
        try {
            // 设置认证上下文
            await vscode.commands.executeCommand('setContext', 'vscode-augment.isLoggedIn', true);
            await vscode.commands.executeCommand('setContext', 'vscode-augment.useOAuth', true);
            await vscode.commands.executeCommand('setContext', 'augment.isAuthenticated', true);
            
            console.log('✅ 扩展上下文已设置');
        } catch (error) {
            console.warn('⚠️ 扩展命令执行失败:', error);
        }
    }
    
    // 方法6: 创建认证事件
    try {
        const authEvent = new CustomEvent('augment-auth-success', {
            detail: {
                accessToken: "_f103ee5c71fadbc084c619a47fa3ac88",
                tenantURL: "https://d17.api.augmentcode.com/",
                timestamp: new Date().toISOString()
            }
        });
        
        window.dispatchEvent(authEvent);
        document.dispatchEvent(authEvent);
        
        console.log('✅ 认证事件已触发');
    } catch (error) {
        console.warn('⚠️ 事件触发失败:', error);
    }
    
    // 方法7: 设置Cookie（如果可能）
    try {
        document.cookie = `augment-session=${encodeURIComponent(JSON.stringify({
            token: "_f103ee5c71fadbc084c619a47fa3ac88",
            tenant: "https://d17.api.augmentcode.com/",
            logged_in: true
        }))};path=/;max-age=86400`;
        
        console.log('✅ Cookie 已设置');
    } catch (error) {
        console.warn('⚠️ Cookie 设置失败:', error);
    }
    
    // 等待一下，然后尝试刷新扩展状态
    setTimeout(() => {
        try {
            // 尝试触发扩展重新检查认证状态
            if (typeof vscode !== 'undefined' && vscode.commands) {
                vscode.commands.executeCommand('augment.refreshAuthStatus');
                vscode.commands.executeCommand('workbench.action.reloadWindow');
            }
            
            console.log('🔄 已尝试刷新扩展状态');
        } catch (error) {
            console.warn('⚠️ 刷新状态失败:', error);
        }
    }, 2000);
    
    console.log('🎉 增强版认证注入完成！');
    console.log('💡 如果仍未生效，请尝试：');
    console.log('   1. 重新加载窗口 (Ctrl+R)');
    console.log('   2. 重启VSCode');
    console.log('   3. 禁用并重新启用Augment扩展');
})();
