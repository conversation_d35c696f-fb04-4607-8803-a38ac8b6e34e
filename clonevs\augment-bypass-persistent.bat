@echo off
title Augment绕过工具 - 认证持久化版本

echo ==========================================
echo    Augment绕过工具 - 认证持久化版本
echo ==========================================
echo.
echo 🎯 完全绕过VSCode硬件指纹检测
echo 🚀 支持最新版VSCode (ES模块兼容)
echo 🔄 支持认证状态持久化
echo 🔒 无需修改VSCode内部文件
echo.

REM 检查是否存在持久化目录
set PERSISTENT_DIR=%~dp0persistent-vscode-data
if not exist "%PERSISTENT_DIR%" (
    echo 📁 创建持久化目录: %PERSISTENT_DIR%
    mkdir "%PERSISTENT_DIR%"
)

REM 生成完全随机的环境
set /a RANDOM_NUM=%RANDOM% * 32768 + %RANDOM%
set FINGERPRINT_HASH=%RANDOM_NUM%%TIME:~6,2%%DATE:~6,4%

echo 🎲 本次会话指纹: %FINGERPRINT_HASH%
echo 📁 持久化目录: %PERSISTENT_DIR%
echo.

REM 设置所有可能的硬件指纹环境变量
set VSCODE_MACHINE_ID=%FINGERPRINT_HASH%abcdef1234567890
set MACHINE_ID=%FINGERPRINT_HASH%abcdef1234567890
set ELECTRON_MACHINE_ID=%FINGERPRINT_HASH%abcdef1234567890
set SYSTEM_UUID=%FINGERPRINT_HASH%-1234-5678-9abc-def012345678
set MAC_ADDRESS=02:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%

REM 伪造系统信息
set COMPUTERNAME=PC-%RANDOM%
set USERNAME=User%RANDOM%
set PROCESSOR_IDENTIFIER=Random CPU %RANDOM%

REM 安全设置
set ELECTRON_NO_SECURITY_WARN=1
set ELECTRON_DISABLE_SECURITY_WARNINGS=1

REM 检查是否存在认证配置
set AUTH_CONFIG=%~dp0..\login\augment-auth-config.json
if exist "%AUTH_CONFIG%" (
    echo 🔑 发现认证配置文件，将自动注入认证信息
    set HAS_AUTH=1
) else (
    echo ⚠️  未发现认证配置，需要手动登录
    set HAS_AUTH=0
)

REM 启动VSCode
echo 🚀 启动持久化绕过版VSCode...
echo.

"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe" ^
  --user-data-dir="%PERSISTENT_DIR%" ^
  --no-sandbox ^
  --disable-web-security ^
  --disable-extensions-except="" ^
  %*

echo.
echo ✅ VSCode已退出
echo 💾 用户数据已保存到持久化目录
echo.
echo 💡 使用提示:
echo    1. 此版本会保持您的认证状态和扩展设置
echo    2. 如需重新开始，删除 persistent-vscode-data 文件夹
echo    3. 认证信息存储在 ../login/augment-auth-config.json
echo.
pause
