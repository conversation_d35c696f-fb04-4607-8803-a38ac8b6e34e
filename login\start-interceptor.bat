@echo off
chcp 65001 >nul
title Augment 认证拦截器

echo.
echo ==========================================
echo   🔐 Augment 认证拦截器启动器
echo ==========================================
echo.

REM 检查Node.js是否可用
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js
    echo 📥 请先下载并安装Node.js: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js 检查通过
echo.

REM 检查拦截器文件是否存在
if not exist "auth-interceptor.js" (
    echo ❌ 错误: 未找到 auth-interceptor.js 文件
    echo 💡 请确保所有文件都在同一目录下
    echo.
    pause
    exit /b 1
)

echo ✅ 拦截器文件检查通过
echo.

REM 询问端口
set /p PORT="请输入服务器端口 (默认3000): "
if "%PORT%"=="" set PORT=3000

echo.
echo 🚀 正在启动认证拦截服务器...
echo 📍 端口: %PORT%
echo 🌐 访问地址: http://localhost:%PORT%
echo.
echo ⚠️  重要提示:
echo    1. 请在浏览器中打开上述地址
echo    2. 当Augment要求登录时，复制认证URL到网页中处理
echo    3. 按 Ctrl+C 可以停止服务器
echo.

REM 启动服务器
node auth-interceptor.js %PORT%

pause 