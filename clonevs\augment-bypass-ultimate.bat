@echo off
title Augment绕过工具 - 一键解决方案

echo ==========================================
echo      Augment绕过工具 - 终极解决方案
echo ==========================================
echo.
echo 🎯 完全绕过VSCode硬件指纹检测
echo 🚀 支持最新版VSCode (ES模块兼容)
echo ⚡ 每次启动都是全新设备
echo 🔒 无需修改VSCode内部文件
echo.

REM 生成完全随机的环境
set /a RANDOM_NUM=%RANDOM% * 32768 + %RANDOM%
set FINGERPRINT_HASH=%RANDOM_NUM%%TIME:~6,2%%DATE:~6,4%

echo 🎲 本次会话指纹: %FINGERPRINT_HASH%
echo.

REM 设置所有可能的硬件指纹环境变量
set VSCODE_MACHINE_ID=%FINGERPRINT_HASH%abcdef1234567890
set MACHINE_ID=%FINGERPRINT_HASH%abcdef1234567890
set ELECTRON_MACHINE_ID=%FINGERPRINT_HASH%abcdef1234567890
set SYSTEM_UUID=%FINGERPRINT_HASH%-1234-5678-9abc-def012345678
set MAC_ADDRESS=02:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%

REM 伪造系统信息
set COMPUTERNAME=PC-%RANDOM%
set USERNAME=User%RANDOM%
set PROCESSOR_IDENTIFIER=Random CPU %RANDOM%

REM 安全设置
set ELECTRON_NO_SECURITY_WARN=1
set ELECTRON_DISABLE_SECURITY_WARNINGS=1

REM 创建随机用户目录
set USER_DATA_DIR=%TEMP%\vscode-bypass-%RANDOM%-%TIME:~6,2%
echo 📁 用户数据目录: %USER_DATA_DIR%

REM 启动VSCode
echo 🚀 启动绕过版VSCode...
echo.

"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe" ^
  --user-data-dir="%USER_DATA_DIR%" ^
  --no-sandbox ^
  --disable-web-security ^
  --disable-extensions-except="" ^
  %*

echo.
echo ✅ VSCode已退出
echo 🧹 正在清理临时文件...

REM 清理
if exist "%USER_DATA_DIR%" rmdir /s /q "%USER_DATA_DIR%" 2>nul

echo ✅ 清理完成！每次运行都是全新设备！
echo.
echo 💡 使用提示:
echo    1. 每次运行此脚本启动VSCode
echo    2. 在VSCode中安装Augment插件
echo    3. 注册新账号即可获得7天试用
echo    4. 试用到期后重新运行脚本
echo.
pause
