/**
 * Augment 专用清理工具
 * 只清理 Augment 相关的数据，保持其他设置不变
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class AugmentOnlyCleaner {
    constructor() {
        this.vscodeUserDataDir = path.join(process.env.APPDATA, 'Code');
        this.vscodeInstallPath = 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code';
        this.vscodeExe = path.join(this.vscodeInstallPath, 'Code.exe');
        this.augmentExtensionId = 'augment.vscode-augment';
    }

    /**
     * 生成新的机器 ID
     */
    generateNewMachineId() {
        return crypto.randomBytes(32).toString('hex');
    }

    /**
     * 备份并更新机器 ID
     */
    updateMachineId() {
        const machineIdFile = path.join(this.vscodeUserDataDir, 'machineid');
        const backupFile = path.join(this.vscodeUserDataDir, 'machineid.original');
        
        // 备份原始 ID
        if (fs.existsSync(machineIdFile) && !fs.existsSync(backupFile)) {
            fs.copyFileSync(machineIdFile, backupFile);
            console.log('✅ 原始机器 ID 已备份');
        }
        
        // 生成新 ID
        const newMachineId = this.generateNewMachineId();
        
        // 确保目录存在
        if (!fs.existsSync(this.vscodeUserDataDir)) {
            fs.mkdirSync(this.vscodeUserDataDir, { recursive: true });
        }
        
        // 写入新 ID
        fs.writeFileSync(machineIdFile, newMachineId);
        console.log('✅ 新机器 ID 已设置:', newMachineId.substring(0, 16) + '...');
        
        return newMachineId;
    }

    /**
     * 清理 Augment 扩展存储
     */
    cleanAugmentStorage() {
        const globalStorageDir = path.join(this.vscodeUserDataDir, 'User', 'globalStorage');
        const augmentStorageDir = path.join(globalStorageDir, this.augmentExtensionId);
        
        if (fs.existsSync(augmentStorageDir)) {
            try {
                fs.rmSync(augmentStorageDir, { recursive: true, force: true });
                console.log('✅ 已清理 Augment 全局存储');
            } catch (error) {
                console.warn('⚠️ 清理 Augment 全局存储失败:', error.message);
            }
        }

        // 清理工作区存储中的 Augment 数据
        const workspaceStorageDir = path.join(this.vscodeUserDataDir, 'User', 'workspaceStorage');
        
        if (fs.existsSync(workspaceStorageDir)) {
            try {
                const workspaces = fs.readdirSync(workspaceStorageDir);
                
                workspaces.forEach(workspace => {
                    const workspacePath = path.join(workspaceStorageDir, workspace);
                    const augmentWorkspaceDir = path.join(workspacePath, this.augmentExtensionId);
                    
                    if (fs.existsSync(augmentWorkspaceDir)) {
                        fs.rmSync(augmentWorkspaceDir, { recursive: true, force: true });
                        console.log('✅ 已清理工作区 Augment 存储:', workspace);
                    }
                });
            } catch (error) {
                console.warn('⚠️ 清理工作区存储失败:', error.message);
            }
        }
    }

    /**
     * 清理 Augment 相关的配置文件
     */
    cleanAugmentConfigs() {
        const userDir = path.join(this.vscodeUserDataDir, 'User');
        
        // 检查并清理 settings.json 中的 Augment 配置
        const settingsFile = path.join(userDir, 'settings.json');
        if (fs.existsSync(settingsFile)) {
            try {
                const settingsContent = fs.readFileSync(settingsFile, 'utf8');
                const settings = JSON.parse(settingsContent);
                
                // 移除 Augment 相关设置
                const augmentKeys = Object.keys(settings).filter(key => 
                    key.toLowerCase().includes('augment')
                );
                
                if (augmentKeys.length > 0) {
                    // 备份原始设置
                    fs.copyFileSync(settingsFile, settingsFile + '.backup');
                    
                    // 移除 Augment 设置
                    augmentKeys.forEach(key => {
                        delete settings[key];
                    });
                    
                    // 写回文件
                    fs.writeFileSync(settingsFile, JSON.stringify(settings, null, 2));
                    console.log('✅ 已清理 settings.json 中的 Augment 配置');
                }
            } catch (error) {
                console.warn('⚠️ 处理 settings.json 失败:', error.message);
            }
        }

        // 清理状态数据库中的 Augment 数据
        const stateFile = path.join(userDir, 'state.vscdb');
        if (fs.existsSync(stateFile)) {
            // SQLite 文件比较复杂，我们创建一个标记文件来提醒手动清理
            const reminderFile = path.join(userDir, 'augment-state-cleanup-reminder.txt');
            fs.writeFileSync(reminderFile, 
                'Augment 状态数据可能仍在 state.vscdb 中\n' +
                '如果需要完全清理，请考虑删除此文件\n' +
                '删除前请备份重要数据'
            );
            console.log('💡 已创建状态清理提醒文件');
        }
    }

    /**
     * 清理浏览器存储中的 Augment 数据
     */
    cleanBrowserStorage() {
        const userDir = path.join(this.vscodeUserDataDir, 'User');
        
        // 清理 Local Storage
        const localStorageDir = path.join(userDir, 'Local Storage');
        if (fs.existsSync(localStorageDir)) {
            try {
                const files = fs.readdirSync(localStorageDir);
                files.forEach(file => {
                    if (file.toLowerCase().includes('augment')) {
                        const filePath = path.join(localStorageDir, file);
                        fs.unlinkSync(filePath);
                        console.log('✅ 已清理 Local Storage:', file);
                    }
                });
            } catch (error) {
                console.warn('⚠️ 清理 Local Storage 失败:', error.message);
            }
        }

        // 清理 Session Storage
        const sessionStorageDir = path.join(userDir, 'Session Storage');
        if (fs.existsSync(sessionStorageDir)) {
            try {
                const files = fs.readdirSync(sessionStorageDir);
                files.forEach(file => {
                    if (file.toLowerCase().includes('augment')) {
                        const filePath = path.join(sessionStorageDir, file);
                        fs.unlinkSync(filePath);
                        console.log('✅ 已清理 Session Storage:', file);
                    }
                });
            } catch (error) {
                console.warn('⚠️ 清理 Session Storage 失败:', error.message);
            }
        }
    }

    /**
     * 创建启动脚本
     */
    createLaunchScript(newMachineId) {
        const scriptContent = `@echo off
title VSCode with Clean Augment State

echo ==========================================
echo   VSCode with Clean Augment State
echo ==========================================
echo.
echo 🆔 New Machine ID: ${newMachineId.substring(0, 16)}...
echo 🧹 Augment data cleared
echo 💾 Other extensions preserved
echo.

REM Set environment variables
set VSCODE_MACHINE_ID=${newMachineId}
set MACHINE_ID=${newMachineId}
set ELECTRON_MACHINE_ID=${newMachineId}

REM Generate random system info for extra protection
set /a RANDOM_NUM=%RANDOM% * 32768 + %RANDOM%
set COMPUTERNAME=PC-%RANDOM_NUM%
set USERNAME=User%RANDOM%
set MAC_ADDRESS=02:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%

echo 🚀 Starting VSCode with clean Augment state...
echo.

"${this.vscodeExe}" %*

echo.
echo ✅ VSCode session ended
echo 💡 Augment should see this as a new device
echo 🔄 You can now login to Augment directly
echo.
pause
`;

        const scriptPath = path.join(__dirname, 'vscode-clean-augment.bat');
        fs.writeFileSync(scriptPath, scriptContent);
        
        console.log('✅ 启动脚本已创建:', scriptPath);
        return scriptPath;
    }

    /**
     * 执行 Augment 专用清理
     */
    async execute() {
        console.log('🧹 Augment 专用清理工具\n');

        console.log('🆔 更新机器 ID...');
        const newMachineId = this.updateMachineId();

        console.log('🗂️ 清理 Augment 扩展存储...');
        this.cleanAugmentStorage();

        console.log('⚙️ 清理 Augment 配置...');
        this.cleanAugmentConfigs();

        console.log('🌐 清理浏览器存储...');
        this.cleanBrowserStorage();

        console.log('📝 创建启动脚本...');
        const scriptPath = this.createLaunchScript(newMachineId);

        console.log('\n🎉 Augment 专用清理完成！\n');
        
        console.log('📋 使用方法：');
        console.log('  1. 双击运行 vscode-clean-augment.bat');
        console.log('  2. VSCode 启动后直接在 Augment 扩展中登录');
        console.log('  3. 认证 URL 会正常工作');
        console.log('');
        console.log('✅ 优势：');
        console.log('  🎯 只清理 Augment 相关数据');
        console.log('  💾 保持所有其他扩展和设置');
        console.log('  🔗 认证 URL 直接可用');
        console.log('  ⚡ 最小化影响');

        return true;
    }

    /**
     * 恢复原始机器 ID
     */
    restore() {
        const machineIdFile = path.join(this.vscodeUserDataDir, 'machineid');
        const backupFile = path.join(this.vscodeUserDataDir, 'machineid.original');
        
        if (fs.existsSync(backupFile)) {
            fs.copyFileSync(backupFile, machineIdFile);
            console.log('✅ 原始机器 ID 已恢复');
            return true;
        } else {
            console.log('❌ 未找到原始机器 ID 备份');
            return false;
        }
    }
}

// 主程序
async function main() {
    const args = process.argv.slice(2);
    const action = args[0] || 'clean';
    
    const cleaner = new AugmentOnlyCleaner();
    
    try {
        if (action === 'restore') {
            console.log('🔄 恢复原始机器 ID...');
            cleaner.restore();
        } else {
            await cleaner.execute();
        }
    } catch (error) {
        console.error('❌ 执行失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = AugmentOnlyCleaner;
