
// 硬件指纹虚拟化注入脚本
(function() {
    console.log('🎭 正在虚拟化硬件指纹...');
    
    const originalFingerprint = '181b728e5a9681be39986d481163c642cd4a26dfae45ab113130abf4a26def90';
    const originalMac = 'ba:55:57:aa:d4:7d';
    
    // 重写 navigator 对象的硬件相关属性
    if (typeof navigator !== 'undefined') {
        // 虚拟化用户代理
        Object.defineProperty(navigator, 'userAgent', {
            get: function() {
                return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.85.0 Chrome/114.0.5735.289 Electron/25.8.4 Safari/537.36';
            },
            configurable: true
        });
        
        // 虚拟化硬件并发
        Object.defineProperty(navigator, 'hardwareConcurrency', {
            get: function() {
                return Math.floor(Math.random() * 8) + 4; // 4-12 cores
            },
            configurable: true
        });
        
        // 虚拟化平台信息
        Object.defineProperty(navigator, 'platform', {
            get: function() {
                return 'Win32';
            },
            configurable: true
        });
    }
    
    // 重写 screen 对象
    if (typeof screen !== 'undefined') {
        const randomWidth = 1920 + Math.floor(Math.random() * 560); // 1920-2480
        const randomHeight = 1080 + Math.floor(Math.random() * 360); // 1080-1440
        
        Object.defineProperty(screen, 'width', {
            get: function() { return randomWidth; },
            configurable: true
        });
        
        Object.defineProperty(screen, 'height', {
            get: function() { return randomHeight; },
            configurable: true
        });
        
        Object.defineProperty(screen, 'availWidth', {
            get: function() { return randomWidth; },
            configurable: true
        });
        
        Object.defineProperty(screen, 'availHeight', {
            get: function() { return randomHeight - 40; },
            configurable: true
        });
    }
    
    // 虚拟化时区
    if (typeof Intl !== 'undefined' && Intl.DateTimeFormat) {
        const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
        Intl.DateTimeFormat.prototype.resolvedOptions = function() {
            const options = originalResolvedOptions.call(this);
            const timezones = ['America/New_York', 'Europe/London', 'Asia/Tokyo', 'Australia/Sydney'];
            options.timeZone = timezones[Math.floor(Math.random() * timezones.length)];
            return options;
        };
    }
    
    // 虚拟化 WebGL 指纹
    if (typeof WebGLRenderingContext !== 'undefined') {
        const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            if (parameter === this.RENDERER) {
                const renderers = [
                    'ANGLE (NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0)',
                    'ANGLE (AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0)',
                    'ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)'
                ];
                return renderers[Math.floor(Math.random() * renderers.length)];
            }
            if (parameter === this.VENDOR) {
                return 'Google Inc. (ANGLE)';
            }
            return originalGetParameter.call(this, parameter);
        };
    }
    
    // 虚拟化 Canvas 指纹
    if (typeof HTMLCanvasElement !== 'undefined') {
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        HTMLCanvasElement.prototype.toDataURL = function() {
            const result = originalToDataURL.apply(this, arguments);
            // 添加随机噪声
            const noise = Math.random().toString(36).substring(2, 8);
            return result.replace(/data:image\/png;base64,/, `data:image/png;base64,${noise}`);
        };
    }
    
    // 设置虚拟机器ID环境变量
    if (typeof process !== 'undefined' && process.env) {
        process.env.VSCODE_MACHINE_ID = originalFingerprint;
        process.env.MACHINE_ID = originalFingerprint;
        process.env.ELECTRON_MACHINE_ID = originalFingerprint;
        process.env.MAC_ADDRESS = originalMac;
    }
    
    // 存储虚拟指纹到 localStorage
    if (typeof localStorage !== 'undefined') {
        localStorage.setItem('virtual-machine-id', originalFingerprint);
        localStorage.setItem('virtual-mac-address', originalMac);
        localStorage.setItem('virtual-fingerprint-timestamp', Date.now().toString());
    }
    
    console.log('✅ 硬件指纹虚拟化完成');
    console.log('🎲 虚拟机器ID:', originalFingerprint.substring(0, 16) + '...');
    console.log('🌐 虚拟MAC地址:', originalMac);
})();
