@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo   Augment VSCode 认证回调处理工具
echo ==========================================
echo.

REM 检查是否提供了认证URL参数
if "%~1"=="" (
    echo 使用方法：
    echo   handle-auth.bat "vscode://augment.vscode-augment/auth/result?code=...&state=...&tenant_url=..."
    echo.
    echo 或者直接运行此脚本，然后手动输入认证URL：
    echo.
    set /p AUTH_URL="请输入认证URL: "
) else (
    set AUTH_URL=%~1
)

REM 检查Node.js是否可用
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo.
echo 🔄 正在处理认证回调...
echo.

REM 调用Node.js脚本处理认证
node auth-handler.js "%AUTH_URL%"

if errorlevel 1 (
    echo.
    echo ❌ 处理失败
) else (
    echo.
    echo ✅ 处理完成！
    echo.
    echo 📁 认证配置已保存到: augment-auth-config.json
    echo 💡 您现在可以将此配置用于虚拟VSCode环境
)

echo.
pause 