/**
 * Augment VSCode 认证回调处理工具
 * 用于在虚拟VSCode环境中处理认证回调
 */

const url = require('url');
const querystring = require('querystring');

class AugmentAuthHandler {
    constructor() {
        this.extensionId = 'augment.vscode-augment';
    }

    /**
     * 解析认证回调URL
     * @param {string} authUrl - 完整的认证回调URL
     * @returns {object} 解析后的认证参数
     */
    parseAuthUrl(authUrl) {
        try {
            const parsedUrl = url.parse(authUrl);
            
            // 验证URL格式
            if (parsedUrl.protocol !== 'vscode:' || 
                parsedUrl.hostname !== this.extensionId) {
                throw new Error('Invalid auth URL format');
            }

            // 解析查询参数
            const queryParams = querystring.parse(parsedUrl.query);
            
            return {
                protocol: parsedUrl.protocol,
                authority: parsedUrl.hostname,
                path: parsedUrl.pathname,
                code: queryParams.code,
                state: queryParams.state,
                tenant_url: queryParams.tenant_url,
                originalUrl: authUrl
            };
        } catch (error) {
            console.error('解析认证URL失败:', error.message);
            return null;
        }
    }

    /**
     * 处理认证结果
     * @param {string} authUrl - 认证回调URL
     * @returns {Promise<object>} 处理结果
     */
    async handleAuthCallback(authUrl) {
        const authData = this.parseAuthUrl(authUrl);
        
        if (!authData) {
            return { success: false, error: '无效的认证URL' };
        }

        console.log('认证回调数据:', {
            code: authData.code,
            state: authData.state,
            tenant_url: authData.tenant_url
        });

        // 这里您需要根据具体需求处理认证数据
        // 例如：发送到您的虚拟VSCode扩展或保存到本地
        
        return {
            success: true,
            data: authData,
            message: '认证回调处理成功'
        };
    }

    /**
     * 生成用于虚拟VSCode的认证配置
     * @param {object} authData - 解析后的认证数据
     * @returns {string} JSON配置字符串
     */
    generateVirtualVSCodeConfig(authData) {
        const config = {
            extensionId: this.extensionId,
            authResult: {
                code: authData.code,
                state: authData.state,
                tenant_url: authData.tenant_url,
                timestamp: new Date().toISOString()
            }
        };

        return JSON.stringify(config, null, 2);
    }
}

// 使用示例
function main() {
    const handler = new AugmentAuthHandler();
    
    // 从命令行参数获取认证URL
    const authUrl = process.argv[2];
    
    if (!authUrl) {
        console.log('使用方法: node auth-handler.js "vscode://augment.vscode-augment/auth/result?code=...&state=...&tenant_url=..."');
        process.exit(1);
    }

    handler.handleAuthCallback(authUrl).then(result => {
        if (result.success) {
            console.log('✅ 认证处理成功!');
            console.log('认证配置:');
            console.log(handler.generateVirtualVSCodeConfig(result.data));
            
            // 可选：将配置保存到文件
            const fs = require('fs');
            fs.writeFileSync('augment-auth-config.json', handler.generateVirtualVSCodeConfig(result.data));
            console.log('配置已保存到 augment-auth-config.json');
        } else {
            console.error('❌ 认证处理失败:', result.error);
        }
    }).catch(error => {
        console.error('❌ 处理过程中出现错误:', error);
    });
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = AugmentAuthHandler; 