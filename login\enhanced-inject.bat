@echo off
chcp 65001 >nul
title Augment 增强版认证注入器

echo.
echo ==========================================
echo   🚀 Augment 增强版认证注入器
echo ==========================================
echo.

REM 检查Node.js是否可用
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js
    echo 📥 请先下载并安装Node.js: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js 检查通过
echo.

REM 检查认证配置文件
if not exist "augment-auth-config.json" (
    echo ❌ 错误: 未找到认证配置文件
    echo 💡 请先运行认证拦截器处理认证URL
    echo.
    pause
    exit /b 1
)

echo ✅ 认证配置文件检查通过
echo.

REM 运行增强版注入器
echo 🔄 正在生成增强版注入脚本...
node enhanced-auth-injector.js

echo.
echo 🎉 增强版注入脚本已准备完成！
echo.
echo 📋 下一步操作：
echo   1. 运行 ../clonevs/augment-bypass-persistent.bat 启动VSCode
echo   2. 在VSCode中按F12打开开发者工具
echo   3. 复制粘贴 augment-enhanced-injection.js 的内容到Console
echo   4. 等待注入完成后按 Ctrl+R 重新加载窗口
echo.

pause
