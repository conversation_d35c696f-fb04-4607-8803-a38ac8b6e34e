@echo off
title Enhanced Auth Injector

echo.
echo ==========================================
echo   Enhanced Auth Injector for Augment
echo ==========================================
echo.

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found
    echo Please install Node.js from: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo Node.js check passed
echo.

REM Check auth config file
if not exist "augment-auth-config.json" (
    echo ERROR: Auth config file not found
    echo Please run auth interceptor first to process auth URL
    echo.
    pause
    exit /b 1
)

echo Auth config file check passed
echo.

REM Run enhanced injector
echo Generating enhanced injection script...
node enhanced-auth-injector.js

echo.
echo Enhanced injection script ready!
echo.
echo Next steps:
echo   1. Run ../clonevs/augment-bypass-persistent.bat to start VSCode
echo   2. Press F12 in VSCode to open Developer Tools
echo   3. Copy and paste augment-enhanced-injection.js content to Console
echo   4. Wait for injection to complete then press Ctrl+R to reload
echo.

pause
