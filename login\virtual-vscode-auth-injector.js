/**
 * 虚拟VSCode Augment认证注入器
 * 用于将认证配置注入到虚拟VSCode环境中
 */

const fs = require('fs');
const path = require('path');

class VirtualVSCodeAuthInjector {
    constructor() {
        this.configFileName = 'augment-auth-config.json';
        this.extensionId = 'augment.vscode-augment';
        this.secretsStorageKey = 'AugmentLoginSession'; // 这是AL的实际值
    }

    /**
     * 读取认证配置文件
     * @returns {object|null} 认证配置数据
     */
    loadAuthConfig() {
        try {
            if (!fs.existsSync(this.configFileName)) {
                console.log('❌ 认证配置文件不存在:', this.configFileName);
                return null;
            }

            const configData = fs.readFileSync(this.configFileName, 'utf8');
            const config = JSON.parse(configData);
            
            console.log('✅ 成功读取认证配置');
            return config;
        } catch (error) {
            console.error('❌ 读取认证配置失败:', error.message);
            return null;
        }
    }

    /**
     * 生成VSCode扩展Secrets存储格式的数据
     * @param {object} authConfig - 认证配置
     * @returns {object} VSCode Secrets格式的数据
     */
    generateVSCodeSecretsData(authConfig) {
        const authResult = authConfig.authResult;
        
        // 根据扩展代码分析，存储格式应该是：
        // {accessToken: string, tenantURL: string, scopes: array}
        const secretsData = {
            accessToken: authResult.code, // 使用认证码作为访问令牌
            tenantURL: authResult.tenant_url,
            scopes: ['augment:read', 'augment:write'] // 默认作用域
        };

        return secretsData;
    }

    /**
     * 生成VSCode工作区状态数据
     * @param {object} authConfig - 认证配置
     * @returns {object} 工作区状态数据
     */
    generateWorkspaceState(authConfig) {
        return {
            'vscode-augment.isLoggedIn': true,
            'vscode-augment.userTier': 'professional',
            'vscode-augment.lastAuthTime': new Date().toISOString(),
            'vscode-augment.authState': authConfig.authResult.state
        };
    }

    /**
     * 生成用于虚拟VSCode的扩展配置
     * @param {object} authConfig - 认证配置
     * @returns {object} 完整的扩展配置
     */
    generateExtensionConfig(authConfig) {
        const secretsData = this.generateVSCodeSecretsData(authConfig);
        const workspaceState = this.generateWorkspaceState(authConfig);

        return {
            secrets: {
                [this.secretsStorageKey]: JSON.stringify(secretsData)
            },
            globalState: {
                'vscode-augment.isLoggedIn': true,
                'hasTrackedInstall': true,
                'lastEnabledExtensionVersion': '0.516.0'
            },
            workspaceState: workspaceState,
            settings: {
                'augment.completions.enableAutomaticCompletions': true,
                'augment.completions.enableQuickSuggestions': true,
                'augment.enableEmptyFileHint': true
            }
        };
    }

    /**
     * 生成用于注入的JavaScript代码
     * @param {object} extensionConfig - 扩展配置
     * @returns {string} 注入代码
     */
    generateInjectionScript(extensionConfig) {
        return `
// Augment认证注入脚本 - 自动生成
(function() {
    console.log('🔧 正在注入Augment认证信息...');
    
    // 模拟VSCode的secrets API
    if (typeof vscode !== 'undefined' && vscode.context && vscode.context.secrets) {
        const secrets = ${JSON.stringify(extensionConfig.secrets, null, 2)};
        
        // 注入secrets
        for (const [key, value] of Object.entries(secrets)) {
            try {
                // 模拟异步存储
                if (vscode.context.secrets.store) {
                    vscode.context.secrets.store(key, value);
                }
                console.log('✅ 已注入Secret:', key);
            } catch (error) {
                console.warn('⚠️ 注入Secret失败:', key, error);
            }
        }
    }

    // 模拟VSCode的globalState API
    if (typeof vscode !== 'undefined' && vscode.context && vscode.context.globalState) {
        const globalState = ${JSON.stringify(extensionConfig.globalState, null, 2)};
        
        // 注入全局状态
        for (const [key, value] of Object.entries(globalState)) {
            try {
                if (vscode.context.globalState.update) {
                    vscode.context.globalState.update(key, value);
                }
                console.log('✅ 已注入GlobalState:', key);
            } catch (error) {
                console.warn('⚠️ 注入GlobalState失败:', key, error);
            }
        }
    }

    // 模拟VSCode的workspaceState API
    if (typeof vscode !== 'undefined' && vscode.context && vscode.context.workspaceState) {
        const workspaceState = ${JSON.stringify(extensionConfig.workspaceState, null, 2)};
        
        // 注入工作区状态
        for (const [key, value] of Object.entries(workspaceState)) {
            try {
                if (vscode.context.workspaceState.update) {
                    vscode.context.workspaceState.update(key, value);
                }
                console.log('✅ 已注入WorkspaceState:', key);
            } catch (error) {
                console.warn('⚠️ 注入WorkspaceState失败:', key, error);
            }
        }
    }

    // 设置上下文变量
    if (typeof vscode !== 'undefined' && vscode.commands) {
        try {
            vscode.commands.executeCommand('setContext', 'vscode-augment.isLoggedIn', true);
            vscode.commands.executeCommand('setContext', 'vscode-augment.useOAuth', true);
            console.log('✅ 已设置认证上下文');
        } catch (error) {
            console.warn('⚠️ 设置上下文失败:', error);
        }
    }

    console.log('🎉 Augment认证信息注入完成！');
})();
`;
    }

    /**
     * 处理认证配置并生成注入文件
     * @returns {boolean} 是否成功
     */
    async injectAuth() {
        console.log('🔄 开始处理Augment认证注入...\n');

        // 1. 读取认证配置
        const authConfig = this.loadAuthConfig();
        if (!authConfig) {
            return false;
        }

        console.log('📋 认证配置详情:');
        console.log('  - 认证码:', authConfig.authResult.code ? '***已设置***' : '未设置');
        console.log('  - 状态ID:', authConfig.authResult.state);
        console.log('  - 租户URL:', authConfig.authResult.tenant_url);
        console.log('  - 时间戳:', authConfig.authResult.timestamp);
        console.log();

        // 2. 生成扩展配置
        const extensionConfig = this.generateExtensionConfig(authConfig);

        // 3. 生成注入脚本
        const injectionScript = this.generateInjectionScript(extensionConfig);

        // 4. 保存注入脚本
        const scriptFileName = 'augment-auth-injection.js';
        fs.writeFileSync(scriptFileName, injectionScript);
        console.log('✅ 已生成注入脚本:', scriptFileName);

        // 5. 保存完整配置（用于调试）
        const fullConfigFileName = 'augment-vscode-config.json';
        fs.writeFileSync(fullConfigFileName, JSON.stringify(extensionConfig, null, 2));
        console.log('✅ 已生成完整配置文件:', fullConfigFileName);

        // 6. 显示使用说明
        this.showUsageInstructions();

        return true;
    }

    /**
     * 显示使用说明
     */
    showUsageInstructions() {
        console.log('\n📖 使用说明:');
        console.log('');
        console.log('方法一：直接注入 (推荐)');
        console.log('  1. 在虚拟VSCode中打开开发者工具 (F12)');
        console.log('  2. 切换到Console标签');
        console.log('  3. 复制粘贴 augment-auth-injection.js 的内容并执行');
        console.log('');
        console.log('方法二：通过扩展设置');
        console.log('  1. 在虚拟VSCode中打开设置');
        console.log('  2. 搜索 "augment"');
        console.log('  3. 使用 augment-vscode-config.json 中的配置信息');
        console.log('');
        console.log('方法三：环境变量注入');
        console.log('  1. 设置环境变量:');
        console.log(`     AUGMENT_ACCESS_TOKEN="${authConfig.authResult.code}"`);
        console.log(`     AUGMENT_TENANT_URL="${authConfig.authResult.tenant_url}"`);
        console.log('  2. 重启虚拟VSCode');
        console.log('');
        console.log('⚠️  注意事项:');
        console.log('  - 认证信息包含敏感数据，请妥善保管');
        console.log('  - 如果注入后仍未生效，请重启VSCode扩展');
        console.log('  - 部分功能可能需要网络连接到Augment服务器');
    }

    /**
     * 验证认证配置是否完整
     * @param {object} config - 认证配置
     * @returns {boolean} 是否有效
     */
    validateAuthConfig(config) {
        if (!config || !config.authResult) {
            console.error('❌ 配置格式无效');
            return false;
        }

        const required = ['code', 'state', 'tenant_url'];
        const authResult = config.authResult;

        for (const field of required) {
            if (!authResult[field]) {
                console.error(`❌ 缺少必需字段: ${field}`);
                return false;
            }
        }

        return true;
    }
}

// 命令行使用
async function main() {
    const injector = new VirtualVSCodeAuthInjector();
    
    console.log('==========================================');
    console.log('  🔐 Augment 虚拟VSCode认证注入器');
    console.log('==========================================\n');

    const success = await injector.injectAuth();
    
    if (success) {
        console.log('\n🎉 认证注入准备完成！');
        console.log('现在您可以在虚拟VSCode中使用生成的注入脚本了。');
    } else {
        console.log('\n❌ 认证注入失败！');
        console.log('请确保已通过认证处理工具生成了配置文件。');
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 运行过程中出现错误:', error);
        process.exit(1);
    });
}

module.exports = VirtualVSCodeAuthInjector; 