@echo off
chcp 65001 >nul
title Augment 虚拟VSCode认证注入器

echo.
echo ==========================================
echo   🔐 Augment 虚拟VSCode认证注入器
echo ==========================================
echo.

REM 检查Node.js是否可用
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js
    echo 📥 请先下载并安装Node.js: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js 检查通过

REM 检查必需文件是否存在
if not exist "virtual-vscode-auth-injector.js" (
    echo ❌ 错误: 未找到 virtual-vscode-auth-injector.js 文件
    echo 💡 请确保所有文件都在同一目录下
    echo.
    pause
    exit /b 1
)

if not exist "augment-auth-config.json" (
    echo ❌ 错误: 未找到认证配置文件 augment-auth-config.json
    echo 💡 请先使用认证处理工具生成配置文件
    echo.
    echo 📝 生成配置文件的步骤:
    echo   1. 运行 start-interceptor.bat 启动认证拦截器
    echo   2. 在浏览器中处理认证URL
    echo   3. 或使用 handle-auth.bat "认证URL" 直接处理
    echo.
    pause
    exit /b 1
)

echo ✅ 文件检查通过
echo.

echo 🔄 正在生成认证注入脚本...
echo.

REM 运行注入器
node virtual-vscode-auth-injector.js

if errorlevel 1 (
    echo.
    echo ❌ 注入脚本生成失败
    echo 请检查认证配置文件是否正确
) else (
    echo.
    echo 🎉 认证注入脚本生成成功！
    echo.
    echo 📋 生成的文件:
    echo   📄 augment-auth-injection.js  - 注入脚本
    echo   📄 augment-vscode-config.json - 完整配置
    echo.
    echo 🚀 下一步操作:
    echo   1. 在虚拟VSCode中按F12打开开发者工具
    echo   2. 切换到Console标签
    echo   3. 复制粘贴augment-auth-injection.js的内容并执行
    echo.
    echo 💡 提示: 执行后查看控制台输出确认注入结果
)

echo.
pause 