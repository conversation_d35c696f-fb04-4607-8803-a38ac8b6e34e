@echo off
title Augment绕过工具 - 环境变量绕过版

echo ==========================================  
echo   Augment绕过工具 - 环境变量绕过版
echo ==========================================
echo.
echo 🎲 随机设备指纹: c8d68aa88b8675c6...
echo 📁 随机用户目录: %TEMP%\vscode-random-1753701863701-w7ix4r
echo 🚀 绕过方式: 环境变量 + 启动参数
echo.

REM === 设置随机环境变量 ===
echo 🔧 设置随机环境变量...

REM 核心硬件指纹环境变量
set VSCODE_MACHINE_ID=c8d68aa88b8675c68a97cd516091796febe936ba9bfacccb01bf97ad8b060a53
set MACHINE_ID=c8d68aa88b8675c68a97cd516091796febe936ba9bfacccb01bf97ad8b060a53
set ELECTRON_MACHINE_ID=c8d68aa88b8675c68a97cd516091796febe936ba9bfacccb01bf97ad8b060a53

REM 随机MAC地址
set MAC_ADDRESS=b2:42:a8:8a:68:a1
set ETHERNET_MAC=ac:24:a6:b9:70:5d

REM 随机主机信息
set COMPUTERNAME=PC-Y1C2VR
set USERNAME=User5456

REM 随机系统信息
set PROCESSOR_IDENTIFIER=Random CPU 362
set PROCESSOR_REVISION=1c1f

REM Electron安全设置
set ELECTRON_NO_SECURITY_WARN=1
set ELECTRON_DISABLE_SECURITY_WARNINGS=1
set ELECTRON_NO_SANDBOX=1

REM VSCode特定设置
set VSCODE_DEV=1
set VSCODE_CLI=1
set VSCODE_LOGS=%TEMP%\vscode-logs-1753701863701

echo ✅ 环境变量设置完成
echo.

REM === 创建随机用户数据目录 ===
set RANDOM_USER_DATA=%TEMP%\vscode-random-1753701863701-w7ix4r
echo 📁 创建用户数据目录: %RANDOM_USER_DATA%
if not exist "%RANDOM_USER_DATA%" mkdir "%RANDOM_USER_DATA%"

REM === 启动VSCode ===
echo 🚀 启动随机化VSCode...
echo.

"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe" ^
  --user-data-dir="%RANDOM_USER_DATA%" ^
  --no-sandbox ^
  --disable-web-security ^
  --disable-features=VizDisplayCompositor ^
  --disable-gpu-sandbox ^
  --disable-software-rasterizer ^
  --disable-background-timer-throttling ^
  --disable-backgrounding-occluded-windows ^
  --disable-renderer-backgrounding ^
  --enable-logging ^
  --log-level=0 ^
  %*

echo.
echo ✅ VSCode已退出
echo 💡 提示: 每次运行都是全新设备，可以重新试用Augment

REM === 清理临时文件 ===
echo 🧹 清理临时文件...
if exist "%RANDOM_USER_DATA%" (
    echo 删除用户数据目录: %RANDOM_USER_DATA%
    rmdir /s /q "%RANDOM_USER_DATA%" 2>nul
)

echo.
echo 🎉 清理完成！
pause
