@echo off
title Augment绕过工具 - 环境变量绕过版

echo ==========================================  
echo   Augment绕过工具 - 环境变量绕过版
echo ==========================================
echo.
echo 🎲 随机设备指纹: 6e2efb1656779554...
echo 📁 随机用户目录: %TEMP%\vscode-random-1753704429292-yputzq
echo 🚀 绕过方式: 环境变量 + 启动参数
echo.

REM === 设置随机环境变量 ===
echo 🔧 设置随机环境变量...

REM 核心硬件指纹环境变量
set VSCODE_MACHINE_ID=6e2efb16567795542bd7f9e7ebcaa8f817a89bf206324dfa1230d7ca9addf708
set MACHINE_ID=6e2efb16567795542bd7f9e7ebcaa8f817a89bf206324dfa1230d7ca9addf708
set ELECTRON_MACHINE_ID=6e2efb16567795542bd7f9e7ebcaa8f817a89bf206324dfa1230d7ca9addf708

REM 随机MAC地址
set MAC_ADDRESS=74:75:b4:1e:f5:dc
set ETHERNET_MAC=74:72:62:f1:1f:70

REM 随机主机信息
set COMPUTERNAME=PC-5FWZ17
set USERNAME=User2540

REM 随机系统信息
set PROCESSOR_IDENTIFIER=Random CPU 6604
set PROCESSOR_REVISION=225a

REM Electron安全设置
set ELECTRON_NO_SECURITY_WARN=1
set ELECTRON_DISABLE_SECURITY_WARNINGS=1
set ELECTRON_NO_SANDBOX=1

REM VSCode特定设置
set VSCODE_DEV=1
set VSCODE_CLI=1
set VSCODE_LOGS=%TEMP%\vscode-logs-1753704429292

echo ✅ 环境变量设置完成
echo.

REM === 创建随机用户数据目录 ===
set RANDOM_USER_DATA=%TEMP%\vscode-random-1753704429292-yputzq
echo 📁 创建用户数据目录: %RANDOM_USER_DATA%
if not exist "%RANDOM_USER_DATA%" mkdir "%RANDOM_USER_DATA%"

REM === 启动VSCode ===
echo 🚀 启动随机化VSCode...
echo.

"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe" ^
  --user-data-dir="%RANDOM_USER_DATA%" ^
  --no-sandbox ^
  --disable-web-security ^
  --disable-features=VizDisplayCompositor ^
  --disable-gpu-sandbox ^
  --disable-software-rasterizer ^
  --disable-background-timer-throttling ^
  --disable-backgrounding-occluded-windows ^
  --disable-renderer-backgrounding ^
  --enable-logging ^
  --log-level=0 ^
  %*

echo.
echo ✅ VSCode已退出
echo 💡 提示: 每次运行都是全新设备，可以重新试用Augment

REM === 清理临时文件 ===
echo 🧹 清理临时文件...
if exist "%RANDOM_USER_DATA%" (
    echo 删除用户数据目录: %RANDOM_USER_DATA%
    rmdir /s /q "%RANDOM_USER_DATA%" 2>nul
)

echo.
echo 🎉 清理完成！
pause
