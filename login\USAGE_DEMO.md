# 🚀 Augment 虚拟VSCode认证使用演示

本文档演示如何完整地在虚拟VSCode环境中使用Augment扩展。

## 📋 前置准备

确保您已有以下环境：
- ✅ Node.js (版本12+)
- ✅ 虚拟VSCode环境
- ✅ Augment扩展已安装但未登录
- ✅ 下载了所有工具文件

## 🎯 完整演示流程

### 第一步：获取认证回调URL

1. **在虚拟VSCode中尝试登录Augment**
   - 点击Augment扩展的"Sign In"按钮
   - 浏览器会打开Augment登录页面

2. **完成登录后获取回调URL**
   - 登录成功后，系统会生成类似这样的URL：
   ```
   vscode://augment.vscode-augment/auth/result?code=_f103ee5c71fadbc084c619a47fa3ac88&state=75b66fdb-5bfc-4f39-b60e-b675283947f7&tenant_url=https%3A%2F%2Fd17.api.augmentcode.com%2F
   ```
   - **不要让系统自动处理这个URL！** 复制完整URL备用

### 第二步：处理认证回调

#### 选项A：使用Web界面（推荐）

1. **启动认证拦截器**
   ```bash
   # 双击运行或命令行执行
   start-interceptor.bat
   ```

2. **打开Web界面**
   - 浏览器访问 `http://localhost:3000`
   - 看到友好的控制面板

3. **处理认证URL**
   - 将复制的认证URL粘贴到输入框
   - 点击"🔄 处理认证"按钮
   - 看到成功消息和认证信息

#### 选项B：使用命令行

```bash
# 直接处理认证URL
handle-auth.bat "vscode://augment.vscode-augment/auth/result?code=...&state=...&tenant_url=..."
```

### 第三步：验证配置文件生成

处理完成后，确认生成了 `augment-auth-config.json` 文件：

```json
{
  "extensionId": "augment.vscode-augment",
  "authResult": {
    "code": "认证代码",
    "state": "状态标识",
    "tenant_url": "租户URL",
    "timestamp": "处理时间戳"
  }
}
```

### 第四步：生成注入脚本

1. **运行注入器**
   ```bash
   # 使用批处理脚本
   inject-auth.bat
   
   # 或直接使用Node.js
   node virtual-vscode-auth-injector.js
   ```

2. **确认生成的文件**
   - ✅ `augment-auth-injection.js` - 注入脚本
   - ✅ `augment-vscode-config.json` - 完整配置

### 第五步：在虚拟VSCode中注入认证

1. **打开开发者工具**
   - 在虚拟VSCode中按 `F12`
   - 如果F12不生效，尝试菜单：Help > Toggle Developer Tools

2. **切换到Console标签**
   - 点击开发者工具中的"Console"标签

3. **执行注入脚本**
   - 打开 `augment-auth-injection.js` 文件
   - 复制全部内容
   - 粘贴到VSCode的Console中
   - 按Enter执行

4. **查看注入结果**
   - 控制台应显示类似输出：
   ```
   🔧 正在注入Augment认证信息...
   ✅ 已注入Secret: AugmentLoginSession
   ✅ 已注入GlobalState: vscode-augment.isLoggedIn
   ✅ 已设置认证上下文
   🎉 Augment认证信息注入完成！
   ```

### 第六步：验证认证成功

1. **检查Augment扩展状态**
   - 查看VSCode左侧Augment图标
   - 应显示已登录状态
   - 不再显示"Sign In"按钮

2. **测试功能**
   - 尝试打开Augment Chat面板
   - 测试代码补全功能
   - 使用Next Edit等功能

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 认证URL获取失败
**问题**：无法获取到认证回调URL
**解决方案**：
- 确保在正确的浏览器中登录Augment
- 检查网络连接
- 尝试清除浏览器缓存后重新登录

#### 2. 配置文件生成失败
**问题**：`augment-auth-config.json` 未生成
**解决方案**：
- 检查Node.js是否正确安装
- 确保认证URL格式正确
- 查看错误信息并修正

#### 3. 注入脚本执行失败
**问题**：在VSCode Console中执行脚本报错
**解决方案**：
- 确保复制了完整的脚本内容
- 检查VSCode是否支持开发者工具
- 尝试分段执行脚本

#### 4. 扩展仍显示未登录
**问题**：注入后Augment扩展仍要求登录
**解决方案**：
- 重启VSCode扩展：Ctrl+Shift+P > "Reload Window"
- 检查注入日志是否有错误
- 验证配置数据格式是否正确

#### 5. 功能部分可用
**问题**：某些Augment功能无法使用
**解决方案**：
- 检查网络连接到Augment服务器
- 确认账户权限和订阅状态
- 查看VSCode输出面板的错误信息

## 💡 高级技巧

### 1. 批量处理多个VSCode实例

如果您需要在多个虚拟VSCode中使用：

```bash
# 生成通用注入脚本
node virtual-vscode-auth-injector.js

# 在每个VSCode实例中执行注入脚本
# 复制 augment-auth-injection.js 内容到各个Console
```

### 2. 自动化注入

创建自动化脚本：

```javascript
// auto-inject.js
const fs = require('fs');

// 读取注入脚本
const injectionScript = fs.readFileSync('augment-auth-injection.js', 'utf8');

// 在您的虚拟化环境中自动执行
// 具体实现取决于您的虚拟化平台
```

### 3. 环境变量配置

对于容器化环境：

```bash
# 设置环境变量
export AUGMENT_ACCESS_TOKEN="您的认证码"
export AUGMENT_TENANT_URL="您的租户URL"

# 启动VSCode
code --extensions-dir /path/to/extensions
```

## 🔒 安全注意事项

1. **保护认证信息**
   - 认证配置包含敏感数据
   - 不要提交到版本控制系统
   - 定期更新认证凭据

2. **清理临时文件**
   - 使用完成后删除认证配置文件
   - 清理浏览器中的认证记录
   - 注意日志文件中的敏感信息

3. **网络安全**
   - 确保认证过程在安全网络中进行
   - 避免在公共WiFi下处理认证信息
   - 使用HTTPS连接

## 📞 支持与反馈

如果您在使用过程中遇到问题：

1. 📖 首先查看本文档的故障排除部分
2. 🔍 检查工具生成的日志信息
3. 💬 创建Issue描述问题和环境信息
4. 📧 联系技术支持获取帮助

---

**🎉 恭喜！** 您现在已经成功在虚拟VSCode环境中使用Augment了！ 