2025-07-28 20:08:21.155 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-28 20:08:21.171 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-28 20:08:21.171 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-28 20:08:21.589 [info] Started local extension host with pid 18828.
2025-07-28 20:08:22.398 [info] ChatSessionStore: Migrating 0 chat sessions from storage service to file system
2025-07-28 20:08:22.660 [error] Ignoring clangd.onConfigChanged.forceEnable as clangd.onConfigChanged is "prompt"
2025-07-28 20:08:22.872 [error] Ignoring clangd.onConfigChanged.forceEnable as clangd.onConfigChanged is "prompt"
2025-07-28 20:08:23.732 [info] Settings Sync: Account status changed from uninitialized to unavailable
2025-07-28 20:08:25.449 [info] [perf] Render performance baseline is 32ms
2025-07-28 20:08:27.839 [info] Extension host (LocalProcess pid: 18828) is unresponsive.
2025-07-28 20:08:29.240 [info] UNRESPONSIVE extension host: starting to profile NOW
2025-07-28 20:08:30.669 [error] [Extension Host] (node:18828) ExperimentalWarning: SQLite is an experimental feature and might change at any time
(Use `Code --trace-warnings ...` to show where the warning was created)
2025-07-28 20:08:31.007 [info] Extension host (LocalProcess pid: 18828) is responsive.
2025-07-28 20:08:31.008 [info] UNRESPONSIVE extension host: received responsive event and cancelling profiling session
2025-07-28 20:08:31.143 [warning] UNRESPONSIVE extension host: 'github.copilot' took 62.***************% of 1109.402ms, saved PROFILE here: 'file:///c%3A/Users/<USER>/AppData/Local/Temp/exthost-228e68.cpuprofile'
2025-07-28 20:11:33.552 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-28 20:11:33.553 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-28 20:11:33.553 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-28 20:11:34.170 [info] Started local extension host with pid 11052.
2025-07-28 20:11:34.647 [error] Ignoring clangd.onConfigChanged.forceEnable as clangd.onConfigChanged is "prompt"
2025-07-28 20:11:35.155 [error] Ignoring clangd.onConfigChanged.forceEnable as clangd.onConfigChanged is "prompt"
2025-07-28 20:11:36.848 [info] Settings Sync: Account status changed from uninitialized to unavailable
2025-07-28 20:11:37.352 [error] [Extension Host] (node:11052) ExperimentalWarning: SQLite is an experimental feature and might change at any time
(Use `Code --trace-warnings ...` to show where the warning was created)
2025-07-28 20:11:39.595 [info] [perf] Render performance baseline is 149ms
2025-07-28 20:11:43.467 [info] Extension host (LocalProcess pid: 11052) is unresponsive.
2025-07-28 20:11:44.469 [info] UNRESPONSIVE extension host: starting to profile NOW
2025-07-28 20:11:45.096 [info] Extension host (LocalProcess pid: 11052) is responsive.
2025-07-28 20:11:45.097 [info] UNRESPONSIVE extension host: received responsive event and cancelling profiling session
2025-07-28 20:11:45.539 [warning] UNRESPONSIVE extension host: 'github.copilot' took 57.**************% of 387.75ms, saved PROFILE here: 'file:///c%3A/Users/<USER>/AppData/Local/Temp/exthost-ece0ad.cpuprofile'
2025-07-28 20:11:47.231 [error] CodeExpectedError: No default agent registered
    at XSe.activateDefaultAgent (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:2047:63665)
2025-07-28 20:13:27.659 [info] Started local extension host with pid 20608.
2025-07-28 20:13:32.937 [error] [Extension Host] (node:20608) ExperimentalWarning: SQLite is an experimental feature and might change at any time
(Use `Code --trace-warnings ...` to show where the warning was created)
2025-07-28 20:13:47.889 [error] command 'vscode-augment.focusAugmentPanel' not found
2025-07-28 20:14:22.808 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-28 20:14:22.809 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-28 20:14:22.810 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-28 20:14:23.131 [warning] Creation of workbench contribution 'workbench.contrib.chatSetup' took 24ms.
2025-07-28 20:14:23.180 [warning] Creation of workbench contribution 'workbench.contrib.userDataProfiles' took 21ms.
2025-07-28 20:14:23.211 [warning] Creation of workbench contribution 'workbench.contrib.configurationDefaultOverridesContribution' took 24ms.
2025-07-28 20:14:24.389 [info] Started local extension host with pid 27436.
2025-07-28 20:14:26.000 [error] Ignoring clangd.onConfigChanged.forceEnable as clangd.onConfigChanged is "prompt"
2025-07-28 20:14:26.606 [error] Ignoring clangd.onConfigChanged.forceEnable as clangd.onConfigChanged is "prompt"
2025-07-28 20:14:30.867 [info] Auto updating outdated extensions. augment.vscode-augment
2025-07-28 20:14:31.015 [info] Settings Sync: Account status changed from uninitialized to unavailable
2025-07-28 20:14:32.277 [info] Extension host (LocalProcess pid: 27436) is unresponsive.
2025-07-28 20:14:32.839 [error] [Extension Host] (node:27436) ExperimentalWarning: SQLite is an experimental feature and might change at any time
(Use `Code --trace-warnings ...` to show where the warning was created)
2025-07-28 20:14:32.841 [info] Extension host (LocalProcess pid: 27436) is responsive.
