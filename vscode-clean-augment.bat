@echo off
title VSCode with Clean Augment State

echo ==========================================
echo   VSCode with Clean Augment State
echo ==========================================
echo.
echo 🆔 New Machine ID: bf328a924973cea0...
echo 🧹 Augment data cleared
echo 💾 Other extensions preserved
echo.

REM Set environment variables
set VSCODE_MACHINE_ID=bf328a924973cea0b3f668471875357179a68e3eb99196363a4d0a6142cddfca
set MACHINE_ID=bf328a924973cea0b3f668471875357179a68e3eb99196363a4d0a6142cddfca
set ELECTRON_MACHINE_ID=bf328a924973cea0b3f668471875357179a68e3eb99196363a4d0a6142cddfca

REM Generate random system info for extra protection
set /a RANDOM_NUM=%RANDOM% * 32768 + %RANDOM%
set COMPUTERNAME=PC-%RANDOM_NUM%
set USERNAME=User%RANDOM%
set MAC_ADDRESS=02:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%

echo 🚀 Starting VSCode with clean Augment state...
echo.

"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe" %*

echo.
echo ✅ VSCode session ended
echo 💡 Augment should see this as a new device
echo 🔄 You can now login to Augment directly
echo.
pause
