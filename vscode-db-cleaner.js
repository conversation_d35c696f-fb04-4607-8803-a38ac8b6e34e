/**
 * VSCode 数据库清理工具
 * 专门清理可能包含设备指纹的数据库文件
 */

const fs = require('fs');
const path = require('path');

class VSCodeDatabaseCleaner {
    constructor() {
        this.vscodeUserDataDir = path.join(process.env.APPDATA, 'Code');
        this.userDir = path.join(this.vscodeUserDataDir, 'User');
    }

    /**
     * 扫描并显示所有数据库文件
     */
    scanDatabases() {
        console.log('🔍 扫描 VSCode 数据库文件...\n');

        const dbFiles = [];

        // SQLite 数据库文件
        const sqliteFiles = [
            'state.vscdb',
            'state.vscdb-shm', 
            'state.vscdb-wal'
        ];

        sqliteFiles.forEach(file => {
            const filePath = path.join(this.userDir, file);
            if (fs.existsSync(filePath)) {
                const stats = fs.statSync(filePath);
                dbFiles.push({
                    type: 'SQLite',
                    name: file,
                    path: filePath,
                    size: this.formatFileSize(stats.size),
                    modified: stats.mtime.toISOString()
                });
            }
        });

        // JSON 配置文件
        const jsonFiles = [
            'extensions.json',
            'extensionsIdentifiers.json',
            'settings.json',
            'keybindings.json'
        ];

        jsonFiles.forEach(file => {
            const filePath = path.join(this.userDir, file);
            if (fs.existsSync(filePath)) {
                const stats = fs.statSync(filePath);
                dbFiles.push({
                    type: 'JSON',
                    name: file,
                    path: filePath,
                    size: this.formatFileSize(stats.size),
                    modified: stats.mtime.toISOString()
                });
            }
        });

        // 扫描数据库目录
        const dbDirs = [
            'IndexedDB',
            'databases', 
            'Local Storage',
            'Session Storage',
            'globalStorage',
            'workspaceStorage'
        ];

        dbDirs.forEach(dir => {
            const dirPath = path.join(this.userDir, dir);
            if (fs.existsSync(dirPath)) {
                const stats = fs.statSync(dirPath);
                dbFiles.push({
                    type: 'Directory',
                    name: dir,
                    path: dirPath,
                    size: this.getDirSize(dirPath),
                    modified: stats.mtime.toISOString()
                });
            }
        });

        return dbFiles;
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 获取目录大小
     */
    getDirSize(dirPath) {
        try {
            let totalSize = 0;
            const files = fs.readdirSync(dirPath);
            
            files.forEach(file => {
                const filePath = path.join(dirPath, file);
                const stats = fs.statSync(filePath);
                
                if (stats.isDirectory()) {
                    totalSize += this.getDirSizeRecursive(filePath);
                } else {
                    totalSize += stats.size;
                }
            });
            
            return this.formatFileSize(totalSize);
        } catch (error) {
            return 'Unknown';
        }
    }

    getDirSizeRecursive(dirPath) {
        try {
            let totalSize = 0;
            const files = fs.readdirSync(dirPath);
            
            files.forEach(file => {
                const filePath = path.join(dirPath, file);
                const stats = fs.statSync(filePath);
                
                if (stats.isDirectory()) {
                    totalSize += this.getDirSizeRecursive(filePath);
                } else {
                    totalSize += stats.size;
                }
            });
            
            return totalSize;
        } catch (error) {
            return 0;
        }
    }

    /**
     * 检查文件是否包含机器 ID
     */
    checkForMachineId(filePath) {
        try {
            if (path.extname(filePath) === '.json') {
                const content = fs.readFileSync(filePath, 'utf8');
                const machineIdPatterns = [
                    /machineId/i,
                    /machine_id/i,
                    /deviceId/i,
                    /device_id/i,
                    /fingerprint/i,
                    /[a-f0-9]{32,64}/g // 长十六进制字符串
                ];

                return machineIdPatterns.some(pattern => pattern.test(content));
            }
        } catch (error) {
            return false;
        }
        return false;
    }

    /**
     * 深度清理数据库
     */
    deepClean() {
        console.log('🧹 开始深度清理 VSCode 数据库...\n');

        const dbFiles = this.scanDatabases();
        let cleanedCount = 0;

        dbFiles.forEach(db => {
            console.log(`📁 处理: ${db.name} (${db.type}, ${db.size})`);

            try {
                if (db.type === 'Directory') {
                    // 对于目录，检查是否包含敏感数据
                    if (db.name === 'globalStorage' || db.name === 'workspaceStorage') {
                        // 只清理 Augment 相关的存储
                        this.cleanAugmentStorage(db.path);
                    } else {
                        // 完全清理其他数据库目录
                        fs.rmSync(db.path, { recursive: true, force: true });
                        console.log(`  ✅ 已清理目录: ${db.name}`);
                        cleanedCount++;
                    }
                } else {
                    // 对于文件，先备份再删除
                    if (this.checkForMachineId(db.path)) {
                        console.log(`  🔍 发现可能包含机器 ID 的文件: ${db.name}`);
                    }
                    
                    // 备份重要文件
                    if (db.name === 'settings.json' || db.name === 'keybindings.json') {
                        fs.copyFileSync(db.path, db.path + '.backup');
                        console.log(`  💾 已备份: ${db.name}`);
                    } else {
                        fs.unlinkSync(db.path);
                        console.log(`  ✅ 已清理: ${db.name}`);
                        cleanedCount++;
                    }
                }
            } catch (error) {
                console.log(`  ❌ 清理失败: ${db.name} - ${error.message}`);
            }
        });

        console.log(`\n🎉 深度清理完成！共清理了 ${cleanedCount} 个项目`);
    }

    /**
     * 清理 Augment 相关存储
     */
    cleanAugmentStorage(storagePath) {
        try {
            const items = fs.readdirSync(storagePath);
            
            items.forEach(item => {
                const itemPath = path.join(storagePath, item);
                
                if (item.includes('augment') || item.includes('Augment')) {
                    fs.rmSync(itemPath, { recursive: true, force: true });
                    console.log(`  ✅ 已清理 Augment 存储: ${item}`);
                }
            });
        } catch (error) {
            console.log(`  ⚠️ 清理存储失败: ${error.message}`);
        }
    }

    /**
     * 显示数据库信息
     */
    showDatabaseInfo() {
        const dbFiles = this.scanDatabases();
        
        console.log('📊 VSCode 数据库文件信息:\n');
        
        dbFiles.forEach(db => {
            console.log(`📁 ${db.name}`);
            console.log(`   类型: ${db.type}`);
            console.log(`   大小: ${db.size}`);
            console.log(`   修改时间: ${db.modified}`);
            
            if (this.checkForMachineId(db.path)) {
                console.log(`   ⚠️  可能包含机器 ID`);
            }
            
            console.log('');
        });
    }
}

// 主程序
async function main() {
    const args = process.argv.slice(2);
    const action = args[0] || 'info';
    
    const cleaner = new VSCodeDatabaseCleaner();
    
    try {
        switch (action) {
            case 'info':
                cleaner.showDatabaseInfo();
                break;
            case 'clean':
                cleaner.deepClean();
                break;
            case 'scan':
                const dbFiles = cleaner.scanDatabases();
                console.log(`找到 ${dbFiles.length} 个数据库文件/目录`);
                break;
            default:
                console.log('用法:');
                console.log('  node vscode-db-cleaner.js info   # 显示数据库信息');
                console.log('  node vscode-db-cleaner.js clean  # 深度清理数据库');
                console.log('  node vscode-db-cleaner.js scan   # 扫描数据库文件');
        }
    } catch (error) {
        console.error('❌ 执行失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = VSCodeDatabaseCleaner;
