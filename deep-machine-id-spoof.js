/**
 * 深层机器 ID 虚拟化工具
 * 直接修改 VSCode 存储的机器 ID
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class DeepMachineIdSpoofer {
    constructor() {
        this.vscodeUserDataDir = path.join(process.env.APPDATA, 'Code');
        this.vscodeInstallPath = 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code';
        this.vscodeExe = path.join(this.vscodeInstallPath, 'Code.exe');
    }

    /**
     * 生成新的机器 ID
     */
    generateNewMachineId() {
        return crypto.randomBytes(32).toString('hex');
    }

    /**
     * 备份原始机器 ID 文件
     */
    backupOriginalMachineId() {
        const machineIdFile = path.join(this.vscodeUserDataDir, 'machineid');
        const backupFile = path.join(this.vscodeUserDataDir, 'machineid.original');
        
        if (fs.existsSync(machineIdFile) && !fs.existsSync(backupFile)) {
            fs.copyFileSync(machineIdFile, backupFile);
            console.log('✅ 原始机器 ID 已备份');
        }
    }

    /**
     * 设置新的机器 ID
     */
    setNewMachineId(newMachineId) {
        const machineIdFile = path.join(this.vscodeUserDataDir, 'machineid');
        
        // 确保目录存在
        if (!fs.existsSync(this.vscodeUserDataDir)) {
            fs.mkdirSync(this.vscodeUserDataDir, { recursive: true });
        }
        
        // 写入新的机器 ID
        fs.writeFileSync(machineIdFile, newMachineId);
        console.log('✅ 新机器 ID 已设置:', newMachineId.substring(0, 16) + '...');
    }

    /**
     * 清理 VSCode 缓存和状态
     */
    clearVSCodeCache() {
        const cacheDirs = [
            path.join(this.vscodeUserDataDir, 'CachedData'),
            path.join(this.vscodeUserDataDir, 'logs'),
            path.join(this.vscodeUserDataDir, 'User', 'workspaceStorage'),
        ];

        cacheDirs.forEach(dir => {
            if (fs.existsSync(dir)) {
                try {
                    fs.rmSync(dir, { recursive: true, force: true });
                    console.log('✅ 已清理缓存:', path.basename(dir));
                } catch (error) {
                    console.warn('⚠️ 清理缓存失败:', path.basename(dir), error.message);
                }
            }
        });
    }

    /**
     * 清理 VSCode 数据库文件
     */
    clearVSCodeDatabases() {
        const userDir = path.join(this.vscodeUserDataDir, 'User');

        // 清理状态数据库
        const stateFiles = [
            path.join(userDir, 'state.vscdb'),
            path.join(userDir, 'state.vscdb-shm'),
            path.join(userDir, 'state.vscdb-wal'),
        ];

        stateFiles.forEach(file => {
            if (fs.existsSync(file)) {
                try {
                    fs.unlinkSync(file);
                    console.log('✅ 已清理状态数据库:', path.basename(file));
                } catch (error) {
                    console.warn('⚠️ 清理状态数据库失败:', path.basename(file), error.message);
                }
            }
        });

        // 清理 IndexedDB 数据
        const indexedDBDirs = [
            path.join(userDir, 'IndexedDB'),
            path.join(userDir, 'databases'),
            path.join(userDir, 'Local Storage'),
            path.join(userDir, 'Session Storage'),
        ];

        indexedDBDirs.forEach(dir => {
            if (fs.existsSync(dir)) {
                try {
                    fs.rmSync(dir, { recursive: true, force: true });
                    console.log('✅ 已清理数据库目录:', path.basename(dir));
                } catch (error) {
                    console.warn('⚠️ 清理数据库目录失败:', path.basename(dir), error.message);
                }
            }
        });

        // 清理扩展数据库
        const extensionFiles = [
            path.join(userDir, 'extensions.json'),
            path.join(userDir, 'extensionsIdentifiers.json'),
        ];

        extensionFiles.forEach(file => {
            if (fs.existsSync(file)) {
                try {
                    // 备份后删除
                    fs.copyFileSync(file, file + '.backup');
                    fs.unlinkSync(file);
                    console.log('✅ 已清理扩展数据库:', path.basename(file));
                } catch (error) {
                    console.warn('⚠️ 清理扩展数据库失败:', path.basename(file), error.message);
                }
            }
        });
    }

    /**
     * 修改扩展存储中的机器相关数据
     */
    modifyExtensionStorage(newMachineId) {
        const globalStorageDir = path.join(this.vscodeUserDataDir, 'User', 'globalStorage');
        
        if (!fs.existsSync(globalStorageDir)) {
            return;
        }

        // 查找 Augment 扩展的存储
        const augmentStorageDir = path.join(globalStorageDir, 'augment.vscode-augment');
        if (fs.existsSync(augmentStorageDir)) {
            try {
                // 删除 Augment 扩展的存储数据
                fs.rmSync(augmentStorageDir, { recursive: true, force: true });
                console.log('✅ 已清理 Augment 扩展存储');
            } catch (error) {
                console.warn('⚠️ 清理 Augment 存储失败:', error.message);
            }
        }

        // 修改其他可能包含机器 ID 的存储
        try {
            const storageFiles = fs.readdirSync(globalStorageDir);
            storageFiles.forEach(file => {
                const filePath = path.join(globalStorageDir, file);
                if (fs.statSync(filePath).isFile() && file.endsWith('.json')) {
                    try {
                        const content = fs.readFileSync(filePath, 'utf8');
                        if (content.includes('machineId') || content.includes('MACHINE_ID')) {
                            // 备份并清理包含机器 ID 的文件
                            fs.copyFileSync(filePath, filePath + '.backup');
                            fs.unlinkSync(filePath);
                            console.log('✅ 已清理机器 ID 相关文件:', file);
                        }
                    } catch (error) {
                        // 忽略读取错误
                    }
                }
            });
        } catch (error) {
            console.warn('⚠️ 扫描存储文件失败:', error.message);
        }
    }

    /**
     * 创建启动脚本
     */
    createLaunchScript(newMachineId) {
        const scriptContent = `@echo off
title VSCode with New Machine ID

echo ==========================================
echo   VSCode with Completely New Machine ID
echo ==========================================
echo.
echo 🆔 New Machine ID: ${newMachineId.substring(0, 16)}...
echo 🔄 All caches cleared
echo 🧹 Extension storage reset
echo.

REM Set environment variables for extra protection
set VSCODE_MACHINE_ID=${newMachineId}
set MACHINE_ID=${newMachineId}
set ELECTRON_MACHINE_ID=${newMachineId}

REM Generate random system info
set /a RANDOM_NUM=%RANDOM% * 32768 + %RANDOM%
set COMPUTERNAME=PC-%RANDOM_NUM%
set USERNAME=User%RANDOM%
set MAC_ADDRESS=02:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%

echo 🚀 Starting VSCode with new identity...
echo.

"${this.vscodeExe}" %*

echo.
echo ✅ VSCode session ended
echo 💡 Machine ID has been permanently changed
echo 🔄 Augment should see this as a completely new device
echo.
pause
`;

        const scriptPath = path.join(__dirname, 'vscode-new-machine-id.bat');
        fs.writeFileSync(scriptPath, scriptContent);
        
        console.log('✅ 启动脚本已创建:', scriptPath);
        return scriptPath;
    }

    /**
     * 恢复原始机器 ID
     */
    restoreOriginalMachineId() {
        const machineIdFile = path.join(this.vscodeUserDataDir, 'machineid');
        const backupFile = path.join(this.vscodeUserDataDir, 'machineid.original');
        
        if (fs.existsSync(backupFile)) {
            fs.copyFileSync(backupFile, machineIdFile);
            console.log('✅ 原始机器 ID 已恢复');
            return true;
        } else {
            console.log('❌ 未找到原始机器 ID 备份');
            return false;
        }
    }

    /**
     * 执行深层机器 ID 虚拟化
     */
    async execute(action = 'spoof') {
        console.log('🔧 深层机器 ID 虚拟化工具\n');

        if (action === 'restore') {
            console.log('🔄 恢复原始机器 ID...');
            const restored = this.restoreOriginalMachineId();
            if (restored) {
                console.log('✅ 原始机器 ID 已恢复');
            }
            return;
        }

        console.log('🎲 生成新的机器 ID...');
        const newMachineId = this.generateNewMachineId();

        console.log('💾 备份原始机器 ID...');
        this.backupOriginalMachineId();

        console.log('🆔 设置新的机器 ID...');
        this.setNewMachineId(newMachineId);

        console.log('🧹 清理 VSCode 缓存...');
        this.clearVSCodeCache();

        console.log('🗄️ 清理 VSCode 数据库...');
        this.clearVSCodeDatabases();

        console.log('🔄 修改扩展存储...');
        this.modifyExtensionStorage(newMachineId);

        console.log('📝 创建启动脚本...');
        const scriptPath = this.createLaunchScript(newMachineId);

        console.log('\n🎉 深层机器 ID 虚拟化完成！\n');
        
        console.log('📋 使用方法：');
        console.log('  1. 双击运行 vscode-new-machine-id.bat');
        console.log('  2. VSCode 将以全新的机器 ID 启动');
        console.log('  3. 直接在 Augment 扩展中登录');
        console.log('  4. 认证 URL 会正常工作');
        console.log('');
        console.log('🔄 恢复原始 ID：');
        console.log('  node deep-machine-id-spoof.js restore');
        console.log('');
        console.log('⚠️  注意：');
        console.log('  - 这会永久改变 VSCode 的机器 ID');
        console.log('  - 某些扩展可能需要重新配置');
        console.log('  - 已备份原始 ID，可以随时恢复');

        return true;
    }
}

// 主程序
async function main() {
    const args = process.argv.slice(2);
    const action = args[0] || 'spoof';
    
    const spoofer = new DeepMachineIdSpoofer();
    
    try {
        await spoofer.execute(action);
    } catch (error) {
        console.error('❌ 执行失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = DeepMachineIdSpoofer;
