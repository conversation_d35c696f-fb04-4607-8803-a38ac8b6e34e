// VSCode环境变量硬件指纹绕过方案
// 通过环境变量和启动参数完全绕过ES模块问题

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { spawn } = require('child_process');

class VSCodeEnvironmentBypass {
    constructor() {
        this.vscodeInstallPath = 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code';
        this.vscodeExe = path.join(this.vscodeInstallPath, 'Code.exe');
    }
    
    // 生成随机硬件指纹
    generateRandomFingerprint() {
        const randomData = {
            macAddress: this.generateRandomMac(),
            hostname: `PC-${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
            username: `User${Math.floor(Math.random() * 9999)}`,
            timestamp: Date.now(),
            random: crypto.randomBytes(16).toString('hex')
        };
        
        return crypto.createHash('sha256')
            .update(JSON.stringify(randomData))
            .digest('hex');
    }
    
    generateRandomMac() {
        const segments = [];
        segments.push((Math.floor(Math.random() * 128) * 2 + 2).toString(16).padStart(2, '0'));
        for (let i = 1; i < 6; i++) {
            segments.push(Math.floor(Math.random() * 256).toString(16).padStart(2, '0'));
        }
        return segments.join(':');
    }
    
    // 创建环境变量绕过启动脚本
    createEnvironmentBypassScript() {
        const randomFingerprint = this.generateRandomFingerprint();
        const randomUserData = `vscode-random-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
        
        const scriptContent = `@echo off
title Augment绕过工具 - 环境变量绕过版

echo ==========================================  
echo   Augment绕过工具 - 环境变量绕过版
echo ==========================================
echo.
echo 🎲 随机设备指纹: ${randomFingerprint.substring(0, 16)}...
echo 📁 随机用户目录: %TEMP%\\${randomUserData}
echo 🚀 绕过方式: 环境变量 + 启动参数
echo.

REM === 设置随机环境变量 ===
echo 🔧 设置随机环境变量...

REM 核心硬件指纹环境变量
set VSCODE_MACHINE_ID=${randomFingerprint}
set MACHINE_ID=${randomFingerprint}
set ELECTRON_MACHINE_ID=${randomFingerprint}

REM 随机MAC地址
set MAC_ADDRESS=${this.generateRandomMac()}
set ETHERNET_MAC=${this.generateRandomMac()}

REM 随机主机信息
set COMPUTERNAME=PC-${Math.random().toString(36).substring(2, 8).toUpperCase()}
set USERNAME=User${Math.floor(Math.random() * 9999)}

REM 随机系统信息
set PROCESSOR_IDENTIFIER=Random CPU ${Math.floor(Math.random() * 9999)}
set PROCESSOR_REVISION=${Math.floor(Math.random() * 9999).toString(16)}

REM Electron安全设置
set ELECTRON_NO_SECURITY_WARN=1
set ELECTRON_DISABLE_SECURITY_WARNINGS=1
set ELECTRON_NO_SANDBOX=1

REM VSCode特定设置
set VSCODE_DEV=1
set VSCODE_CLI=1
set VSCODE_LOGS=%TEMP%\\vscode-logs-${Date.now()}

echo ✅ 环境变量设置完成
echo.

REM === 创建随机用户数据目录 ===
set RANDOM_USER_DATA=%TEMP%\\${randomUserData}
echo 📁 创建用户数据目录: %RANDOM_USER_DATA%
if not exist "%RANDOM_USER_DATA%" mkdir "%RANDOM_USER_DATA%"

REM === 启动VSCode ===
echo 🚀 启动随机化VSCode...
echo.

"${this.vscodeExe}" ^
  --user-data-dir="%RANDOM_USER_DATA%" ^
  --no-sandbox ^
  --disable-web-security ^
  --disable-features=VizDisplayCompositor ^
  --disable-gpu-sandbox ^
  --disable-software-rasterizer ^
  --disable-background-timer-throttling ^
  --disable-backgrounding-occluded-windows ^
  --disable-renderer-backgrounding ^
  --enable-logging ^
  --log-level=0 ^
  %*

echo.
echo ✅ VSCode已退出
echo 💡 提示: 每次运行都是全新设备，可以重新试用Augment

REM === 清理临时文件 ===
echo 🧹 清理临时文件...
if exist "%RANDOM_USER_DATA%" (
    echo 删除用户数据目录: %RANDOM_USER_DATA%
    rmdir /s /q "%RANDOM_USER_DATA%" 2>nul
)

echo.
echo 🎉 清理完成！
pause
`;
        
        const scriptPath = path.join(__dirname, 'launch-env-bypass.bat');
        fs.writeFileSync(scriptPath, scriptContent);
        
        console.log('✅ 环境变量绕过脚本已创建:', scriptPath);
        return scriptPath;
    }
    
    // 恢复VSCode到原始状态
    async restoreVSCode() {
        try {
            console.log('🔄 恢复VSCode到原始状态...');
            
            const mainFile = path.join(this.vscodeInstallPath, 'resources/app/out/main.js');
            const backupFile = mainFile + '.backup';
            
            if (fs.existsSync(backupFile)) {
                fs.copyFileSync(backupFile, mainFile);
                console.log('✅ 已恢复 main.js');
            }
            
            const injectionFile = path.join(this.vscodeInstallPath, 'resources/app/fingerprint_injection.js');
            if (fs.existsSync(injectionFile)) {
                fs.unlinkSync(injectionFile);
                console.log('✅ 已删除注入文件');
            }
            
            console.log('✅ VSCode已完全恢复到原始状态');
            
        } catch (error) {
            console.error('❌ 恢复失败:', error.message);
        }
    }
    
    // 测试启动VSCode
    async testLaunch() {
        console.log('🧪 测试环境变量绕过启动...');
        
        const randomFingerprint = this.generateRandomFingerprint();
        const userDataDir = path.join(process.env.TEMP, `vscode-test-${Date.now()}`);
        
        const env = {
            ...process.env,
            VSCODE_MACHINE_ID: randomFingerprint,
            MACHINE_ID: randomFingerprint,
            ELECTRON_MACHINE_ID: randomFingerprint,
            MAC_ADDRESS: this.generateRandomMac(),
            COMPUTERNAME: `PC-${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
            USERNAME: `User${Math.floor(Math.random() * 9999)}`,
            ELECTRON_NO_SECURITY_WARN: '1'
        };
        
        const args = [
            '--user-data-dir=' + userDataDir,
            '--no-sandbox',
            '--disable-web-security',
            '--wait'
        ];
        
        console.log('🚀 启动VSCode进行测试...');
        console.log('📱 测试指纹:', randomFingerprint.substring(0, 16) + '...');
        
        try {
            const child = spawn(this.vscodeExe, args, { 
                env: env,
                stdio: 'inherit'
            });
            
            child.on('close', (code) => {
                console.log(`✅ VSCode测试完成，退出代码: ${code}`);
                
                // 清理测试目录
                if (fs.existsSync(userDataDir)) {
                    fs.rmSync(userDataDir, { recursive: true, force: true });
                    console.log('🧹 测试目录已清理');
                }
            });
            
            child.on('error', (error) => {
                console.error('❌ 启动失败:', error.message);
            });
            
        } catch (error) {
            console.error('❌ 测试启动失败:', error.message);
        }
    }
    
    // 创建简化的一键解决方案
    createSimpleBypassScript() {
        const scriptContent = `@echo off
title Augment绕过工具 - 一键解决方案

echo ==========================================  
echo      Augment绕过工具 - 终极解决方案
echo ==========================================
echo.
echo 🎯 完全绕过VSCode硬件指纹检测
echo 🚀 支持最新版VSCode (ES模块兼容)
echo ⚡ 每次启动都是全新设备
echo 🔒 无需修改VSCode内部文件
echo.

REM 生成完全随机的环境
set /a RANDOM_NUM=%RANDOM% * 32768 + %RANDOM%
set FINGERPRINT_HASH=%RANDOM_NUM%%TIME:~6,2%%DATE:~6,4%

echo 🎲 本次会话指纹: %FINGERPRINT_HASH%
echo.

REM 设置所有可能的硬件指纹环境变量
set VSCODE_MACHINE_ID=%FINGERPRINT_HASH%abcdef1234567890
set MACHINE_ID=%FINGERPRINT_HASH%abcdef1234567890  
set ELECTRON_MACHINE_ID=%FINGERPRINT_HASH%abcdef1234567890
set SYSTEM_UUID=%FINGERPRINT_HASH%-1234-5678-9abc-def012345678
set MAC_ADDRESS=02:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%

REM 伪造系统信息
set COMPUTERNAME=PC-%RANDOM%
set USERNAME=User%RANDOM%
set PROCESSOR_IDENTIFIER=Random CPU %RANDOM%

REM 安全设置
set ELECTRON_NO_SECURITY_WARN=1
set ELECTRON_DISABLE_SECURITY_WARNINGS=1

REM 创建随机用户目录
set USER_DATA_DIR=%TEMP%\\vscode-bypass-%RANDOM%-%TIME:~6,2%
echo 📁 用户数据目录: %USER_DATA_DIR%

REM 启动VSCode
echo 🚀 启动绕过版VSCode...
echo.

"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe" ^
  --user-data-dir="%USER_DATA_DIR%" ^
  --no-sandbox ^
  --disable-web-security ^
  --disable-extensions-except="" ^
  %*

echo.
echo ✅ VSCode已退出
echo 🧹 正在清理临时文件...

REM 清理
if exist "%USER_DATA_DIR%" rmdir /s /q "%USER_DATA_DIR%" 2>nul

echo ✅ 清理完成！每次运行都是全新设备！
echo.
echo 💡 使用提示:
echo    1. 每次运行此脚本启动VSCode
echo    2. 在VSCode中安装Augment插件  
echo    3. 注册新账号即可获得7天试用
echo    4. 试用到期后重新运行脚本
echo.
pause
`;
        
        const scriptPath = path.join(__dirname, 'augment-bypass-ultimate.bat');
        fs.writeFileSync(scriptPath, scriptContent);
        
        console.log('✅ 终极绕过脚本已创建:', scriptPath);
        return scriptPath;
    }
}

// 主程序
async function main() {
    const bypass = new VSCodeEnvironmentBypass();
    
    const args = process.argv.slice(2);
    const command = args[0] || 'create';
    
    try {
        switch (command) {
            case 'create':
                console.log('🚀 创建环境变量绕过方案...');
                
                // 1. 恢复VSCode到原始状态
                await bypass.restoreVSCode();
                
                // 2. 创建环境变量绕过脚本
                const scriptPath = bypass.createEnvironmentBypassScript();
                
                // 3. 创建简化版本
                const simpleScript = bypass.createSimpleBypassScript();
                
                console.log('');
                console.log('🎉 绕过方案创建完成！');
                console.log('');
                console.log('📝 使用方法：');
                console.log('   方案1（推荐）: 运行 augment-bypass-ultimate.bat');
                console.log('   方案2（高级）: 运行 launch-env-bypass.bat'); 
                console.log('');
                console.log('✅ 优势：');
                console.log('   🚀 完全绕过ES模块问题');
                console.log('   🎲 每次启动都是全新设备');
                console.log('   🔒 不修改VSCode内部文件');
                console.log('   ⚡ 支持最新版VSCode');
                break;
                
            case 'test':
                await bypass.testLaunch();
                break;
                
            case 'restore':
                await bypass.restoreVSCode();
                break;
                
            default:
                console.log('用法:');
                console.log('  node vscode_env_bypass.js create   # 创建绕过脚本');
                console.log('  node vscode_env_bypass.js test     # 测试启动');
                console.log('  node vscode_env_bypass.js restore  # 恢复VSCode');
        }
    } catch (error) {
        console.error('❌ 执行失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = VSCodeEnvironmentBypass; 