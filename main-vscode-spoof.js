/**
 * 主 VSCode 硬件指纹虚拟化工具
 * 在主 VSCode 中运行，只虚拟化硬件指纹，保持正常功能
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { spawn } = require('child_process');

class MainVSCodeSpoofer {
    constructor() {
        this.vscodeInstallPath = 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code';
        this.vscodeExe = path.join(this.vscodeInstallPath, 'Code.exe');
        this.userDataDir = path.join(process.env.APPDATA, 'Code');
    }

    /**
     * 生成随机硬件指纹
     */
    generateRandomFingerprint() {
        const randomData = {
            macAddress: this.generateRandomMac(),
            hostname: `PC-${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
            username: `User${Math.floor(Math.random() * 9999)}`,
            timestamp: Date.now(),
            random: crypto.randomBytes(16).toString('hex')
        };
        
        return crypto.createHash('sha256')
            .update(JSON.stringify(randomData))
            .digest('hex');
    }

    generateRandomMac() {
        const segments = [];
        segments.push((Math.floor(Math.random() * 128) * 2 + 2).toString(16).padStart(2, '0'));
        for (let i = 1; i < 6; i++) {
            segments.push(Math.floor(Math.random() * 256).toString(16).padStart(2, '0'));
        }
        return segments.join(':');
    }

    /**
     * 创建硬件指纹注入脚本
     */
    createFingerprintInjection() {
        const randomFingerprint = this.generateRandomFingerprint();
        const randomMac = this.generateRandomMac();
        
        const injectionScript = `
// 硬件指纹虚拟化注入脚本
(function() {
    console.log('🎭 正在虚拟化硬件指纹...');
    
    const originalFingerprint = '${randomFingerprint}';
    const originalMac = '${randomMac}';
    
    // 重写 navigator 对象的硬件相关属性
    if (typeof navigator !== 'undefined') {
        // 虚拟化用户代理
        Object.defineProperty(navigator, 'userAgent', {
            get: function() {
                return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.85.0 Chrome/114.0.5735.289 Electron/25.8.4 Safari/537.36';
            },
            configurable: true
        });
        
        // 虚拟化硬件并发
        Object.defineProperty(navigator, 'hardwareConcurrency', {
            get: function() {
                return Math.floor(Math.random() * 8) + 4; // 4-12 cores
            },
            configurable: true
        });
        
        // 虚拟化平台信息
        Object.defineProperty(navigator, 'platform', {
            get: function() {
                return 'Win32';
            },
            configurable: true
        });
    }
    
    // 重写 screen 对象
    if (typeof screen !== 'undefined') {
        const randomWidth = 1920 + Math.floor(Math.random() * 560); // 1920-2480
        const randomHeight = 1080 + Math.floor(Math.random() * 360); // 1080-1440
        
        Object.defineProperty(screen, 'width', {
            get: function() { return randomWidth; },
            configurable: true
        });
        
        Object.defineProperty(screen, 'height', {
            get: function() { return randomHeight; },
            configurable: true
        });
        
        Object.defineProperty(screen, 'availWidth', {
            get: function() { return randomWidth; },
            configurable: true
        });
        
        Object.defineProperty(screen, 'availHeight', {
            get: function() { return randomHeight - 40; },
            configurable: true
        });
    }
    
    // 虚拟化时区
    if (typeof Intl !== 'undefined' && Intl.DateTimeFormat) {
        const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
        Intl.DateTimeFormat.prototype.resolvedOptions = function() {
            const options = originalResolvedOptions.call(this);
            const timezones = ['America/New_York', 'Europe/London', 'Asia/Tokyo', 'Australia/Sydney'];
            options.timeZone = timezones[Math.floor(Math.random() * timezones.length)];
            return options;
        };
    }
    
    // 虚拟化 WebGL 指纹
    if (typeof WebGLRenderingContext !== 'undefined') {
        const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            if (parameter === this.RENDERER) {
                const renderers = [
                    'ANGLE (NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0)',
                    'ANGLE (AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0)',
                    'ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)'
                ];
                return renderers[Math.floor(Math.random() * renderers.length)];
            }
            if (parameter === this.VENDOR) {
                return 'Google Inc. (ANGLE)';
            }
            return originalGetParameter.call(this, parameter);
        };
    }
    
    // 虚拟化 Canvas 指纹
    if (typeof HTMLCanvasElement !== 'undefined') {
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        HTMLCanvasElement.prototype.toDataURL = function() {
            const result = originalToDataURL.apply(this, arguments);
            // 添加随机噪声
            const noise = Math.random().toString(36).substring(2, 8);
            return result.replace(/data:image\\/png;base64,/, \`data:image/png;base64,\${noise}\`);
        };
    }
    
    // 设置虚拟机器ID环境变量
    if (typeof process !== 'undefined' && process.env) {
        process.env.VSCODE_MACHINE_ID = originalFingerprint;
        process.env.MACHINE_ID = originalFingerprint;
        process.env.ELECTRON_MACHINE_ID = originalFingerprint;
        process.env.MAC_ADDRESS = originalMac;
    }
    
    // 存储虚拟指纹到 localStorage
    if (typeof localStorage !== 'undefined') {
        localStorage.setItem('virtual-machine-id', originalFingerprint);
        localStorage.setItem('virtual-mac-address', originalMac);
        localStorage.setItem('virtual-fingerprint-timestamp', Date.now().toString());
    }
    
    console.log('✅ 硬件指纹虚拟化完成');
    console.log('🎲 虚拟机器ID:', originalFingerprint.substring(0, 16) + '...');
    console.log('🌐 虚拟MAC地址:', originalMac);
})();
`;

        const scriptPath = path.join(__dirname, 'fingerprint-injection.js');
        fs.writeFileSync(scriptPath, injectionScript);
        
        console.log('✅ 硬件指纹注入脚本已创建:', scriptPath);
        return { scriptPath, fingerprint: randomFingerprint, mac: randomMac };
    }

    /**
     * 创建主 VSCode 启动脚本
     */
    createMainVSCodeScript() {
        const { fingerprint, mac } = this.createFingerprintInjection();
        
        const scriptContent = `@echo off
title Augment Bypass - Main VSCode with Virtual Fingerprint

echo ==========================================
echo   Augment Bypass - Main VSCode Version
echo ==========================================
echo.
echo 🎯 Using real VSCode with virtual fingerprint
echo 🔗 Auth URLs will work directly
echo 🎭 Hardware fingerprint virtualized
echo.

REM Generate session fingerprint
set SESSION_ID=%RANDOM%%TIME:~6,2%%DATE:~6,4%
echo 🎲 Session ID: %SESSION_ID%

REM Set virtual environment variables
set VSCODE_MACHINE_ID=${fingerprint}
set MACHINE_ID=${fingerprint}
set ELECTRON_MACHINE_ID=${fingerprint}
set MAC_ADDRESS=${mac}
set COMPUTERNAME=PC-%RANDOM%
set USERNAME=VirtualUser%RANDOM%

REM Security settings
set ELECTRON_NO_SECURITY_WARN=1
set ELECTRON_DISABLE_SECURITY_WARNINGS=1

echo 🚀 Starting main VSCode with virtual fingerprint...
echo.

REM Start VSCode with fingerprint injection
"${this.vscodeExe}" ^
  --enable-logging ^
  --log-level=info ^
  %*

echo.
echo ✅ VSCode session ended
echo 💡 Tips:
echo    1. Auth URLs work directly in this mode
echo    2. Augment sees this as a new device
echo    3. All your extensions and settings are preserved
echo.
pause
`;

        const scriptPath = path.join(__dirname, 'main-vscode-bypass.bat');
        fs.writeFileSync(scriptPath, scriptContent);
        
        console.log('✅ 主 VSCode 启动脚本已创建:', scriptPath);
        return scriptPath;
    }

    /**
     * 创建自动注入启动器
     */
    createAutoInjectionLauncher() {
        const scriptContent = `@echo off
title Auto Injection Launcher

echo Starting VSCode with auto fingerprint injection...

REM Start VSCode in background
start "" "${this.vscodeExe}"

REM Wait for VSCode to load
timeout /t 3 /nobreak >nul

REM Inject fingerprint script
echo Injecting fingerprint virtualization...
powershell -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('{F12}'); Start-Sleep -Seconds 2; [System.Windows.Forms.SendKeys]::SendWait('console.log(\\\"Injecting fingerprint...\\\");'); [System.Windows.Forms.SendKeys]::SendWait('{ENTER}');"

echo.
echo VSCode started with fingerprint injection
echo Press F12 and paste fingerprint-injection.js content to complete setup
pause
`;

        const scriptPath = path.join(__dirname, 'auto-injection-launcher.bat');
        fs.writeFileSync(scriptPath, scriptContent);
        
        console.log('✅ 自动注入启动器已创建:', scriptPath);
        return scriptPath;
    }

    /**
     * 主执行函数
     */
    async execute() {
        console.log('🚀 创建主 VSCode 硬件指纹虚拟化方案...\n');

        // 1. 创建指纹注入脚本
        const injectionResult = this.createFingerprintInjection();

        // 2. 创建主 VSCode 启动脚本
        const mainScript = this.createMainVSCodeScript();

        // 3. 创建自动注入启动器
        const autoLauncher = this.createAutoInjectionLauncher();

        console.log('\n🎉 主 VSCode 虚拟化方案创建完成！\n');
        
        console.log('📝 使用方法：');
        console.log('');
        console.log('方案1（推荐）：手动注入');
        console.log('  1. 双击运行 main-vscode-bypass.bat');
        console.log('  2. 在 VSCode 中按 F12 打开开发者工具');
        console.log('  3. 复制粘贴 fingerprint-injection.js 的内容到 Console');
        console.log('  4. 现在可以直接点击 Augment 登录，URL 会正常工作');
        console.log('');
        console.log('方案2：自动启动');
        console.log('  1. 双击运行 auto-injection-launcher.bat');
        console.log('  2. 等待 VSCode 启动后手动完成注入');
        console.log('');
        console.log('✅ 优势：');
        console.log('  🔗 认证 URL 直接工作，无需拦截');
        console.log('  💾 保持所有扩展和设置');
        console.log('  🎭 只虚拟化硬件指纹');
        console.log('  ⚡ 更简单的操作流程');

        return true;
    }
}

// 主程序
async function main() {
    const spoofer = new MainVSCodeSpoofer();
    
    try {
        await spoofer.execute();
    } catch (error) {
        console.error('❌ 执行失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = MainVSCodeSpoofer;
