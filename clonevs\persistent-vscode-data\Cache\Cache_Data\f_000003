{"version": 3, "sources": ["./file:/Users/<USER>/vss/_work/1/s/src/vs/code/electron-browser/workbench/workbench.ts", "vs/code/electron-browser/workbench/workbench.ts"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n/* eslint-disable no-restricted-globals */\n\n(async function () {\n\n\t// Add a perf entry right from the top\n\tperformance.mark('code/didStartRenderer');\n\n\ttype ISandboxConfiguration = import('../../../base/parts/sandbox/common/sandboxTypes.js').ISandboxConfiguration;\n\ttype ILoadResult<M, T extends ISandboxConfiguration> = import('../../../platform/window/electron-browser/window.js').ILoadResult<M, T>;\n\ttype ILoadOptions<T extends ISandboxConfiguration> = import('../../../platform/window/electron-browser/window.js').ILoadOptions<T>;\n\ttype INativeWindowConfiguration = import('../../../platform/window/common/window.ts').INativeWindowConfiguration;\n\ttype IMainWindowSandboxGlobals = import('../../../base/parts/sandbox/electron-browser/globals.js').IMainWindowSandboxGlobals;\n\ttype IDesktopMain = import('../../../workbench/electron-browser/desktop.main.js').IDesktopMain;\n\n\tconst preloadGlobals: IMainWindowSandboxGlobals = (window as any).vscode; // defined by preload.ts\n\tconst safeProcess = preloadGlobals.process;\n\n\t//#region Splash Screen Helpers\n\n\tfunction showSplash(configuration: INativeWindowConfiguration) {\n\t\tperformance.mark('code/willShowPartsSplash');\n\n\t\tlet data = configuration.partsSplash;\n\t\tif (data) {\n\t\t\tif (configuration.autoDetectHighContrast && configuration.colorScheme.highContrast) {\n\t\t\t\tif ((configuration.colorScheme.dark && data.baseTheme !== 'hc-black') || (!configuration.colorScheme.dark && data.baseTheme !== 'hc-light')) {\n\t\t\t\t\tdata = undefined; // high contrast mode has been turned by the OS -> ignore stored colors and layouts\n\t\t\t\t}\n\t\t\t} else if (configuration.autoDetectColorScheme) {\n\t\t\t\tif ((configuration.colorScheme.dark && data.baseTheme !== 'vs-dark') || (!configuration.colorScheme.dark && data.baseTheme !== 'vs')) {\n\t\t\t\t\tdata = undefined; // OS color scheme is tracked and has changed\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// developing an extension -> ignore stored layouts\n\t\tif (data && configuration.extensionDevelopmentPath) {\n\t\t\tdata.layoutInfo = undefined;\n\t\t}\n\n\t\t// minimal color configuration (works with or without persisted data)\n\t\tlet baseTheme;\n\t\tlet shellBackground;\n\t\tlet shellForeground;\n\t\tif (data) {\n\t\t\tbaseTheme = data.baseTheme;\n\t\t\tshellBackground = data.colorInfo.editorBackground;\n\t\t\tshellForeground = data.colorInfo.foreground;\n\t\t} else if (configuration.autoDetectHighContrast && configuration.colorScheme.highContrast) {\n\t\t\tif (configuration.colorScheme.dark) {\n\t\t\t\tbaseTheme = 'hc-black';\n\t\t\t\tshellBackground = '#000000';\n\t\t\t\tshellForeground = '#FFFFFF';\n\t\t\t} else {\n\t\t\t\tbaseTheme = 'hc-light';\n\t\t\t\tshellBackground = '#FFFFFF';\n\t\t\t\tshellForeground = '#000000';\n\t\t\t}\n\t\t} else if (configuration.autoDetectColorScheme) {\n\t\t\tif (configuration.colorScheme.dark) {\n\t\t\t\tbaseTheme = 'vs-dark';\n\t\t\t\tshellBackground = '#1E1E1E';\n\t\t\t\tshellForeground = '#CCCCCC';\n\t\t\t} else {\n\t\t\t\tbaseTheme = 'vs';\n\t\t\t\tshellBackground = '#FFFFFF';\n\t\t\t\tshellForeground = '#000000';\n\t\t\t}\n\t\t}\n\n\t\tconst style = document.createElement('style');\n\t\tstyle.className = 'initialShellColors';\n\t\twindow.document.head.appendChild(style);\n\t\tstyle.textContent = `body {\tbackground-color: ${shellBackground}; color: ${shellForeground}; margin: 0; padding: 0; }`;\n\n\t\t// set zoom level as soon as possible\n\t\tif (typeof data?.zoomLevel === 'number' && typeof preloadGlobals?.webFrame?.setZoomLevel === 'function') {\n\t\t\tpreloadGlobals.webFrame.setZoomLevel(data.zoomLevel);\n\t\t}\n\n\t\t// restore parts if possible (we might not always store layout info)\n\t\tif (data?.layoutInfo) {\n\t\t\tconst { layoutInfo, colorInfo } = data;\n\n\t\t\tconst splash = document.createElement('div');\n\t\t\tsplash.id = 'monaco-parts-splash';\n\t\t\tsplash.className = baseTheme ?? 'vs-dark';\n\n\t\t\tif (layoutInfo.windowBorder && colorInfo.windowBorder) {\n\t\t\t\tconst borderElement = document.createElement('div');\n\t\t\t\tborderElement.style.position = 'absolute';\n\t\t\t\tborderElement.style.width = 'calc(100vw - 2px)';\n\t\t\t\tborderElement.style.height = 'calc(100vh - 2px)';\n\t\t\t\tborderElement.style.zIndex = '1'; // allow border above other elements\n\t\t\t\tborderElement.style.border = `1px solid var(--window-border-color)`;\n\t\t\t\tborderElement.style.setProperty('--window-border-color', colorInfo.windowBorder);\n\n\t\t\t\tif (layoutInfo.windowBorderRadius) {\n\t\t\t\t\tborderElement.style.borderRadius = layoutInfo.windowBorderRadius;\n\t\t\t\t}\n\n\t\t\t\tsplash.appendChild(borderElement);\n\t\t\t}\n\n\t\t\tif (layoutInfo.auxiliaryBarWidth === Number.MAX_SAFE_INTEGER) {\n\t\t\t\t// if auxiliary bar is maximized, it goes as wide as the\n\t\t\t\t// window width but leaving room for activity bar\n\t\t\t\tlayoutInfo.auxiliaryBarWidth = window.innerWidth - layoutInfo.activityBarWidth;\n\t\t\t} else {\n\t\t\t\t// otherwise adjust for other parts sizes if not maximized\n\t\t\t\tlayoutInfo.auxiliaryBarWidth = Math.min(layoutInfo.auxiliaryBarWidth, window.innerWidth - (layoutInfo.activityBarWidth + layoutInfo.editorPartMinWidth + layoutInfo.sideBarWidth));\n\t\t\t}\n\t\t\tlayoutInfo.sideBarWidth = Math.min(layoutInfo.sideBarWidth, window.innerWidth - (layoutInfo.activityBarWidth + layoutInfo.editorPartMinWidth + layoutInfo.auxiliaryBarWidth));\n\n\t\t\t// part: title\n\t\t\tif (layoutInfo.titleBarHeight > 0) {\n\t\t\t\tconst titleDiv = document.createElement('div');\n\t\t\t\ttitleDiv.style.position = 'absolute';\n\t\t\t\ttitleDiv.style.width = '100%';\n\t\t\t\ttitleDiv.style.height = `${layoutInfo.titleBarHeight}px`;\n\t\t\t\ttitleDiv.style.left = '0';\n\t\t\t\ttitleDiv.style.top = '0';\n\t\t\t\ttitleDiv.style.backgroundColor = `${colorInfo.titleBarBackground}`;\n\t\t\t\t(titleDiv.style as any)['-webkit-app-region'] = 'drag';\n\t\t\t\tsplash.appendChild(titleDiv);\n\n\t\t\t\tif (colorInfo.titleBarBorder) {\n\t\t\t\t\tconst titleBorder = document.createElement('div');\n\t\t\t\t\ttitleBorder.style.position = 'absolute';\n\t\t\t\t\ttitleBorder.style.width = '100%';\n\t\t\t\t\ttitleBorder.style.height = '1px';\n\t\t\t\t\ttitleBorder.style.left = '0';\n\t\t\t\t\ttitleBorder.style.bottom = '0';\n\t\t\t\t\ttitleBorder.style.borderBottom = `1px solid ${colorInfo.titleBarBorder}`;\n\t\t\t\t\ttitleDiv.appendChild(titleBorder);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// part: activity bar\n\t\t\tif (layoutInfo.activityBarWidth > 0) {\n\t\t\t\tconst activityDiv = document.createElement('div');\n\t\t\t\tactivityDiv.style.position = 'absolute';\n\t\t\t\tactivityDiv.style.width = `${layoutInfo.activityBarWidth}px`;\n\t\t\t\tactivityDiv.style.height = `calc(100% - ${layoutInfo.titleBarHeight + layoutInfo.statusBarHeight}px)`;\n\t\t\t\tactivityDiv.style.top = `${layoutInfo.titleBarHeight}px`;\n\t\t\t\tif (layoutInfo.sideBarSide === 'left') {\n\t\t\t\t\tactivityDiv.style.left = '0';\n\t\t\t\t} else {\n\t\t\t\t\tactivityDiv.style.right = '0';\n\t\t\t\t}\n\t\t\t\tactivityDiv.style.backgroundColor = `${colorInfo.activityBarBackground}`;\n\t\t\t\tsplash.appendChild(activityDiv);\n\n\t\t\t\tif (colorInfo.activityBarBorder) {\n\t\t\t\t\tconst activityBorderDiv = document.createElement('div');\n\t\t\t\t\tactivityBorderDiv.style.position = 'absolute';\n\t\t\t\t\tactivityBorderDiv.style.width = '1px';\n\t\t\t\t\tactivityBorderDiv.style.height = '100%';\n\t\t\t\t\tactivityBorderDiv.style.top = '0';\n\t\t\t\t\tif (layoutInfo.sideBarSide === 'left') {\n\t\t\t\t\t\tactivityBorderDiv.style.right = '0';\n\t\t\t\t\t\tactivityBorderDiv.style.borderRight = `1px solid ${colorInfo.activityBarBorder}`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tactivityBorderDiv.style.left = '0';\n\t\t\t\t\t\tactivityBorderDiv.style.borderLeft = `1px solid ${colorInfo.activityBarBorder}`;\n\t\t\t\t\t}\n\t\t\t\t\tactivityDiv.appendChild(activityBorderDiv);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// part: side bar\n\t\t\tif (layoutInfo.sideBarWidth > 0) {\n\t\t\t\tconst sideDiv = document.createElement('div');\n\t\t\t\tsideDiv.style.position = 'absolute';\n\t\t\t\tsideDiv.style.width = `${layoutInfo.sideBarWidth}px`;\n\t\t\t\tsideDiv.style.height = `calc(100% - ${layoutInfo.titleBarHeight + layoutInfo.statusBarHeight}px)`;\n\t\t\t\tsideDiv.style.top = `${layoutInfo.titleBarHeight}px`;\n\t\t\t\tif (layoutInfo.sideBarSide === 'left') {\n\t\t\t\t\tsideDiv.style.left = `${layoutInfo.activityBarWidth}px`;\n\t\t\t\t} else {\n\t\t\t\t\tsideDiv.style.right = `${layoutInfo.activityBarWidth}px`;\n\t\t\t\t}\n\t\t\t\tsideDiv.style.backgroundColor = `${colorInfo.sideBarBackground}`;\n\t\t\t\tsplash.appendChild(sideDiv);\n\n\t\t\t\tif (colorInfo.sideBarBorder) {\n\t\t\t\t\tconst sideBorderDiv = document.createElement('div');\n\t\t\t\t\tsideBorderDiv.style.position = 'absolute';\n\t\t\t\t\tsideBorderDiv.style.width = '1px';\n\t\t\t\t\tsideBorderDiv.style.height = '100%';\n\t\t\t\t\tsideBorderDiv.style.top = '0';\n\t\t\t\t\tsideBorderDiv.style.right = '0';\n\t\t\t\t\tif (layoutInfo.sideBarSide === 'left') {\n\t\t\t\t\t\tsideBorderDiv.style.borderRight = `1px solid ${colorInfo.sideBarBorder}`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tsideBorderDiv.style.left = '0';\n\t\t\t\t\t\tsideBorderDiv.style.borderLeft = `1px solid ${colorInfo.sideBarBorder}`;\n\t\t\t\t\t}\n\t\t\t\t\tsideDiv.appendChild(sideBorderDiv);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// part: auxiliary sidebar\n\t\t\tif (layoutInfo.auxiliaryBarWidth > 0) {\n\t\t\t\tconst auxSideDiv = document.createElement('div');\n\t\t\t\tauxSideDiv.style.position = 'absolute';\n\t\t\t\tauxSideDiv.style.width = `${layoutInfo.auxiliaryBarWidth}px`;\n\t\t\t\tauxSideDiv.style.height = `calc(100% - ${layoutInfo.titleBarHeight + layoutInfo.statusBarHeight}px)`;\n\t\t\t\tauxSideDiv.style.top = `${layoutInfo.titleBarHeight}px`;\n\t\t\t\tif (layoutInfo.sideBarSide === 'left') {\n\t\t\t\t\tauxSideDiv.style.right = '0';\n\t\t\t\t} else {\n\t\t\t\t\tauxSideDiv.style.left = '0';\n\t\t\t\t}\n\t\t\t\tauxSideDiv.style.backgroundColor = `${colorInfo.sideBarBackground}`;\n\t\t\t\tsplash.appendChild(auxSideDiv);\n\n\t\t\t\tif (colorInfo.sideBarBorder) {\n\t\t\t\t\tconst auxSideBorderDiv = document.createElement('div');\n\t\t\t\t\tauxSideBorderDiv.style.position = 'absolute';\n\t\t\t\t\tauxSideBorderDiv.style.width = '1px';\n\t\t\t\t\tauxSideBorderDiv.style.height = '100%';\n\t\t\t\t\tauxSideBorderDiv.style.top = '0';\n\t\t\t\t\tif (layoutInfo.sideBarSide === 'left') {\n\t\t\t\t\t\tauxSideBorderDiv.style.left = '0';\n\t\t\t\t\t\tauxSideBorderDiv.style.borderLeft = `1px solid ${colorInfo.sideBarBorder}`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tauxSideBorderDiv.style.right = '0';\n\t\t\t\t\t\tauxSideBorderDiv.style.borderRight = `1px solid ${colorInfo.sideBarBorder}`;\n\t\t\t\t\t}\n\t\t\t\t\tauxSideDiv.appendChild(auxSideBorderDiv);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// part: statusbar\n\t\t\tif (layoutInfo.statusBarHeight > 0) {\n\t\t\t\tconst statusDiv = document.createElement('div');\n\t\t\t\tstatusDiv.style.position = 'absolute';\n\t\t\t\tstatusDiv.style.width = '100%';\n\t\t\t\tstatusDiv.style.height = `${layoutInfo.statusBarHeight}px`;\n\t\t\t\tstatusDiv.style.bottom = '0';\n\t\t\t\tstatusDiv.style.left = '0';\n\t\t\t\tif (configuration.workspace && colorInfo.statusBarBackground) {\n\t\t\t\t\tstatusDiv.style.backgroundColor = colorInfo.statusBarBackground;\n\t\t\t\t} else if (!configuration.workspace && colorInfo.statusBarNoFolderBackground) {\n\t\t\t\t\tstatusDiv.style.backgroundColor = colorInfo.statusBarNoFolderBackground;\n\t\t\t\t}\n\t\t\t\tsplash.appendChild(statusDiv);\n\n\t\t\t\tif (colorInfo.statusBarBorder) {\n\t\t\t\t\tconst statusBorderDiv = document.createElement('div');\n\t\t\t\t\tstatusBorderDiv.style.position = 'absolute';\n\t\t\t\t\tstatusBorderDiv.style.width = '100%';\n\t\t\t\t\tstatusBorderDiv.style.height = '1px';\n\t\t\t\t\tstatusBorderDiv.style.top = '0';\n\t\t\t\t\tstatusBorderDiv.style.borderTop = `1px solid ${colorInfo.statusBarBorder}`;\n\t\t\t\t\tstatusDiv.appendChild(statusBorderDiv);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\twindow.document.body.appendChild(splash);\n\t\t}\n\n\t\tperformance.mark('code/didShowPartsSplash');\n\t}\n\n\t//#endregion\n\n\t//#region Window Helpers\n\n\tasync function load<M, T extends ISandboxConfiguration>(esModule: string, options: ILoadOptions<T>): Promise<ILoadResult<M, T>> {\n\n\t\t// Window Configuration from Preload Script\n\t\tconst configuration = await resolveWindowConfiguration<T>();\n\n\t\t// Signal before import()\n\t\toptions?.beforeImport?.(configuration);\n\n\t\t// Developer settings\n\t\tconst { enableDeveloperKeybindings, removeDeveloperKeybindingsAfterLoad, developerDeveloperKeybindingsDisposable, forceDisableShowDevtoolsOnError } = setupDeveloperKeybindings(configuration, options);\n\n\t\t// NLS\n\t\tsetupNLS<T>(configuration);\n\n\t\t// Compute base URL and set as global\n\t\tconst baseUrl = new URL(`${fileUriFromPath(configuration.appRoot, { isWindows: safeProcess.platform === 'win32', scheme: 'vscode-file', fallbackAuthority: 'vscode-app' })}/out/`);\n\t\tglobalThis._VSCODE_FILE_ROOT = baseUrl.toString();\n\n\t\t// Dev only: CSS import map tricks\n\t\tsetupCSSImportMaps<T>(configuration, baseUrl);\n\n\t\t// ESM Import\n\t\ttry {\n\t\t\tconst result = await import(new URL(`${esModule}.js`, baseUrl).href);\n\n\t\t\tif (developerDeveloperKeybindingsDisposable && removeDeveloperKeybindingsAfterLoad) {\n\t\t\t\tdeveloperDeveloperKeybindingsDisposable();\n\t\t\t}\n\n\t\t\treturn { result, configuration };\n\t\t} catch (error) {\n\t\t\tonUnexpectedError(error, enableDeveloperKeybindings && !forceDisableShowDevtoolsOnError);\n\n\t\t\tthrow error;\n\t\t}\n\t}\n\n\tasync function resolveWindowConfiguration<T extends ISandboxConfiguration>() {\n\t\tconst timeout = setTimeout(() => { console.error(`[resolve window config] Could not resolve window configuration within 10 seconds, but will continue to wait...`); }, 10000);\n\t\tperformance.mark('code/willWaitForWindowConfig');\n\n\t\tconst configuration = await preloadGlobals.context.resolveConfiguration() as T;\n\t\tperformance.mark('code/didWaitForWindowConfig');\n\n\t\tclearTimeout(timeout);\n\n\t\treturn configuration;\n\t}\n\n\tfunction setupDeveloperKeybindings<T extends ISandboxConfiguration>(configuration: T, options: ILoadOptions<T>) {\n\t\tconst {\n\t\t\tforceEnableDeveloperKeybindings,\n\t\t\tdisallowReloadKeybinding,\n\t\t\tremoveDeveloperKeybindingsAfterLoad,\n\t\t\tforceDisableShowDevtoolsOnError\n\t\t} = typeof options?.configureDeveloperSettings === 'function' ? options.configureDeveloperSettings(configuration) : {\n\t\t\tforceEnableDeveloperKeybindings: false,\n\t\t\tdisallowReloadKeybinding: false,\n\t\t\tremoveDeveloperKeybindingsAfterLoad: false,\n\t\t\tforceDisableShowDevtoolsOnError: false\n\t\t};\n\n\t\tconst isDev = !!safeProcess.env['VSCODE_DEV'];\n\t\tconst enableDeveloperKeybindings = Boolean(isDev || forceEnableDeveloperKeybindings);\n\t\tlet developerDeveloperKeybindingsDisposable: Function | undefined = undefined;\n\t\tif (enableDeveloperKeybindings) {\n\t\t\tdeveloperDeveloperKeybindingsDisposable = registerDeveloperKeybindings(disallowReloadKeybinding);\n\t\t}\n\n\t\treturn {\n\t\t\tenableDeveloperKeybindings,\n\t\t\tremoveDeveloperKeybindingsAfterLoad,\n\t\t\tdeveloperDeveloperKeybindingsDisposable,\n\t\t\tforceDisableShowDevtoolsOnError\n\t\t};\n\t}\n\n\tfunction registerDeveloperKeybindings(disallowReloadKeybinding: boolean | undefined): Function {\n\t\tconst ipcRenderer = preloadGlobals.ipcRenderer;\n\n\t\tconst extractKey =\n\t\t\tfunction (e: KeyboardEvent) {\n\t\t\t\treturn [\n\t\t\t\t\te.ctrlKey ? 'ctrl-' : '',\n\t\t\t\t\te.metaKey ? 'meta-' : '',\n\t\t\t\t\te.altKey ? 'alt-' : '',\n\t\t\t\t\te.shiftKey ? 'shift-' : '',\n\t\t\t\t\te.keyCode\n\t\t\t\t].join('');\n\t\t\t};\n\n\t\t// Devtools & reload support\n\t\tconst TOGGLE_DEV_TOOLS_KB = (safeProcess.platform === 'darwin' ? 'meta-alt-73' : 'ctrl-shift-73'); // mac: Cmd-Alt-I, rest: Ctrl-Shift-I\n\t\tconst TOGGLE_DEV_TOOLS_KB_ALT = '123'; // F12\n\t\tconst RELOAD_KB = (safeProcess.platform === 'darwin' ? 'meta-82' : 'ctrl-82'); // mac: Cmd-R, rest: Ctrl-R\n\n\t\tlet listener: ((e: KeyboardEvent) => void) | undefined = function (e) {\n\t\t\tconst key = extractKey(e);\n\t\t\tif (key === TOGGLE_DEV_TOOLS_KB || key === TOGGLE_DEV_TOOLS_KB_ALT) {\n\t\t\t\tipcRenderer.send('vscode:toggleDevTools');\n\t\t\t} else if (key === RELOAD_KB && !disallowReloadKeybinding) {\n\t\t\t\tipcRenderer.send('vscode:reloadWindow');\n\t\t\t}\n\t\t};\n\n\t\twindow.addEventListener('keydown', listener);\n\n\t\treturn function () {\n\t\t\tif (listener) {\n\t\t\t\twindow.removeEventListener('keydown', listener);\n\t\t\t\tlistener = undefined;\n\t\t\t}\n\t\t};\n\t}\n\n\tfunction setupNLS<T extends ISandboxConfiguration>(configuration: T): void {\n\t\tglobalThis._VSCODE_NLS_MESSAGES = configuration.nls.messages;\n\t\tglobalThis._VSCODE_NLS_LANGUAGE = configuration.nls.language;\n\n\t\tlet language = configuration.nls.language || 'en';\n\t\tif (language === 'zh-tw') {\n\t\t\tlanguage = 'zh-Hant';\n\t\t} else if (language === 'zh-cn') {\n\t\t\tlanguage = 'zh-Hans';\n\t\t}\n\n\t\twindow.document.documentElement.setAttribute('lang', language);\n\t}\n\n\tfunction onUnexpectedError(error: string | Error, showDevtoolsOnError: boolean): void {\n\t\tif (showDevtoolsOnError) {\n\t\t\tconst ipcRenderer = preloadGlobals.ipcRenderer;\n\t\t\tipcRenderer.send('vscode:openDevTools');\n\t\t}\n\n\t\tconsole.error(`[uncaught exception]: ${error}`);\n\n\t\tif (error && typeof error !== 'string' && error.stack) {\n\t\t\tconsole.error(error.stack);\n\t\t}\n\t}\n\n\tfunction fileUriFromPath(path: string, config: { isWindows?: boolean; scheme?: string; fallbackAuthority?: string }): string {\n\n\t\t// Since we are building a URI, we normalize any backslash\n\t\t// to slashes and we ensure that the path begins with a '/'.\n\t\tlet pathName = path.replace(/\\\\/g, '/');\n\t\tif (pathName.length > 0 && pathName.charAt(0) !== '/') {\n\t\t\tpathName = `/${pathName}`;\n\t\t}\n\n\t\tlet uri: string;\n\n\t\t// Windows: in order to support UNC paths (which start with '//')\n\t\t// that have their own authority, we do not use the provided authority\n\t\t// but rather preserve it.\n\t\tif (config.isWindows && pathName.startsWith('//')) {\n\t\t\turi = encodeURI(`${config.scheme || 'file'}:${pathName}`);\n\t\t}\n\n\t\t// Otherwise we optionally add the provided authority if specified\n\t\telse {\n\t\t\turi = encodeURI(`${config.scheme || 'file'}://${config.fallbackAuthority || ''}${pathName}`);\n\t\t}\n\n\t\treturn uri.replace(/#/g, '%23');\n\t}\n\n\tfunction setupCSSImportMaps<T extends ISandboxConfiguration>(configuration: T, baseUrl: URL) {\n\n\t\t// DEV ---------------------------------------------------------------------------------------\n\t\t// DEV: This is for development and enables loading CSS via import-statements via import-maps.\n\t\t// DEV: For each CSS modules that we have we defined an entry in the import map that maps to\n\t\t// DEV: a blob URL that loads the CSS via a dynamic @import-rule.\n\t\t// DEV ---------------------------------------------------------------------------------------\n\n\t\tif (Array.isArray(configuration.cssModules) && configuration.cssModules.length > 0) {\n\t\t\tperformance.mark('code/willAddCssLoader');\n\n\t\t\tglobalThis._VSCODE_CSS_LOAD = function (url) {\n\t\t\t\tconst link = document.createElement('link');\n\t\t\t\tlink.setAttribute('rel', 'stylesheet');\n\t\t\t\tlink.setAttribute('type', 'text/css');\n\t\t\t\tlink.setAttribute('href', url);\n\n\t\t\t\twindow.document.head.appendChild(link);\n\t\t\t};\n\n\t\t\tconst importMap: { imports: Record<string, string> } = { imports: {} };\n\t\t\tfor (const cssModule of configuration.cssModules) {\n\t\t\t\tconst cssUrl = new URL(cssModule, baseUrl).href;\n\t\t\t\tconst jsSrc = `globalThis._VSCODE_CSS_LOAD('${cssUrl}');\\n`;\n\t\t\t\tconst blob = new Blob([jsSrc], { type: 'application/javascript' });\n\t\t\t\timportMap.imports[cssUrl] = URL.createObjectURL(blob);\n\t\t\t}\n\n\t\t\tconst ttp = window.trustedTypes?.createPolicy('vscode-bootstrapImportMap', { createScript(value) { return value; }, });\n\t\t\tconst importMapSrc = JSON.stringify(importMap, undefined, 2);\n\t\t\tconst importMapScript = document.createElement('script');\n\t\t\timportMapScript.type = 'importmap';\n\t\t\timportMapScript.setAttribute('nonce', '0c6a828f1297');\n\t\t\t// @ts-ignore\n\t\t\timportMapScript.textContent = ttp?.createScript(importMapSrc) ?? importMapSrc;\n\t\t\twindow.document.head.appendChild(importMapScript);\n\n\t\t\tperformance.mark('code/didAddCssLoader');\n\t\t}\n\t}\n\n\t//#endregion\n\n\tconst { result, configuration } = await load<IDesktopMain, INativeWindowConfiguration>('vs/workbench/workbench.desktop.main',\n\t\t{\n\t\t\tconfigureDeveloperSettings: function (windowConfig) {\n\t\t\t\treturn {\n\t\t\t\t\t// disable automated devtools opening on error when running extension tests\n\t\t\t\t\t// as this can lead to nondeterministic test execution (devtools steals focus)\n\t\t\t\t\tforceDisableShowDevtoolsOnError: typeof windowConfig.extensionTestsPath === 'string' || windowConfig['enable-smoke-test-driver'] === true,\n\t\t\t\t\t// enable devtools keybindings in extension development window\n\t\t\t\t\tforceEnableDeveloperKeybindings: Array.isArray(windowConfig.extensionDevelopmentPath) && windowConfig.extensionDevelopmentPath.length > 0,\n\t\t\t\t\tremoveDeveloperKeybindingsAfterLoad: true\n\t\t\t\t};\n\t\t\t},\n\t\t\tbeforeImport: function (windowConfig) {\n\n\t\t\t\t// Show our splash as early as possible\n\t\t\t\tshowSplash(windowConfig);\n\n\t\t\t\t// Code windows have a `vscodeWindowId` property to identify them\n\t\t\t\tObject.defineProperty(window, 'vscodeWindowId', {\n\t\t\t\t\tget: () => windowConfig.windowId\n\t\t\t\t});\n\n\t\t\t\t// It looks like browsers only lazily enable\n\t\t\t\t// the <canvas> element when needed. Since we\n\t\t\t\t// leverage canvas elements in our code in many\n\t\t\t\t// locations, we try to help the browser to\n\t\t\t\t// initialize canvas when it is idle, right\n\t\t\t\t// before we wait for the scripts to be loaded.\n\t\t\t\twindow.requestIdleCallback(() => {\n\t\t\t\t\tconst canvas = document.createElement('canvas');\n\t\t\t\t\tconst context = canvas.getContext('2d');\n\t\t\t\t\tcontext?.clearRect(0, 0, canvas.width, canvas.height);\n\t\t\t\t\tcanvas.remove();\n\t\t\t\t}, { timeout: 50 });\n\n\t\t\t\t// Track import() perf\n\t\t\t\tperformance.mark('code/willLoadWorkbenchMain');\n\t\t\t}\n\t\t}\n\t);\n\n\t// Mark start of workbench\n\tperformance.mark('code/didLoadWorkbenchMain');\n\n\t// Load workbench\n\tresult.main(configuration);\n}());\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n/* eslint-disable no-restricted-globals */\n\n(async function () {\n\n\t// Add a perf entry right from the top\n\tperformance.mark('code/didStartRenderer');\n\n\ttype ISandboxConfiguration = import('../../../base/parts/sandbox/common/sandboxTypes.js').ISandboxConfiguration;\n\ttype ILoadResult<M, T extends ISandboxConfiguration> = import('../../../platform/window/electron-browser/window.js').ILoadResult<M, T>;\n\ttype ILoadOptions<T extends ISandboxConfiguration> = import('../../../platform/window/electron-browser/window.js').ILoadOptions<T>;\n\ttype INativeWindowConfiguration = import('../../../platform/window/common/window.ts').INativeWindowConfiguration;\n\ttype IMainWindowSandboxGlobals = import('../../../base/parts/sandbox/electron-browser/globals.js').IMainWindowSandboxGlobals;\n\ttype IDesktopMain = import('../../../workbench/electron-browser/desktop.main.js').IDesktopMain;\n\n\tconst preloadGlobals: IMainWindowSandboxGlobals = (window as any).vscode; // defined by preload.ts\n\tconst safeProcess = preloadGlobals.process;\n\n\t//#region Splash Screen Helpers\n\n\tfunction showSplash(configuration: INativeWindowConfiguration) {\n\t\tperformance.mark('code/willShowPartsSplash');\n\n\t\tlet data = configuration.partsSplash;\n\t\tif (data) {\n\t\t\tif (configuration.autoDetectHighContrast && configuration.colorScheme.highContrast) {\n\t\t\t\tif ((configuration.colorScheme.dark && data.baseTheme !== 'hc-black') || (!configuration.colorScheme.dark && data.baseTheme !== 'hc-light')) {\n\t\t\t\t\tdata = undefined; // high contrast mode has been turned by the OS -> ignore stored colors and layouts\n\t\t\t\t}\n\t\t\t} else if (configuration.autoDetectColorScheme) {\n\t\t\t\tif ((configuration.colorScheme.dark && data.baseTheme !== 'vs-dark') || (!configuration.colorScheme.dark && data.baseTheme !== 'vs')) {\n\t\t\t\t\tdata = undefined; // OS color scheme is tracked and has changed\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// developing an extension -> ignore stored layouts\n\t\tif (data && configuration.extensionDevelopmentPath) {\n\t\t\tdata.layoutInfo = undefined;\n\t\t}\n\n\t\t// minimal color configuration (works with or without persisted data)\n\t\tlet baseTheme;\n\t\tlet shellBackground;\n\t\tlet shellForeground;\n\t\tif (data) {\n\t\t\tbaseTheme = data.baseTheme;\n\t\t\tshellBackground = data.colorInfo.editorBackground;\n\t\t\tshellForeground = data.colorInfo.foreground;\n\t\t} else if (configuration.autoDetectHighContrast && configuration.colorScheme.highContrast) {\n\t\t\tif (configuration.colorScheme.dark) {\n\t\t\t\tbaseTheme = 'hc-black';\n\t\t\t\tshellBackground = '#000000';\n\t\t\t\tshellForeground = '#FFFFFF';\n\t\t\t} else {\n\t\t\t\tbaseTheme = 'hc-light';\n\t\t\t\tshellBackground = '#FFFFFF';\n\t\t\t\tshellForeground = '#000000';\n\t\t\t}\n\t\t} else if (configuration.autoDetectColorScheme) {\n\t\t\tif (configuration.colorScheme.dark) {\n\t\t\t\tbaseTheme = 'vs-dark';\n\t\t\t\tshellBackground = '#1E1E1E';\n\t\t\t\tshellForeground = '#CCCCCC';\n\t\t\t} else {\n\t\t\t\tbaseTheme = 'vs';\n\t\t\t\tshellBackground = '#FFFFFF';\n\t\t\t\tshellForeground = '#000000';\n\t\t\t}\n\t\t}\n\n\t\tconst style = document.createElement('style');\n\t\tstyle.className = 'initialShellColors';\n\t\twindow.document.head.appendChild(style);\n\t\tstyle.textContent = `body {\tbackground-color: ${shellBackground}; color: ${shellForeground}; margin: 0; padding: 0; }`;\n\n\t\t// set zoom level as soon as possible\n\t\tif (typeof data?.zoomLevel === 'number' && typeof preloadGlobals?.webFrame?.setZoomLevel === 'function') {\n\t\t\tpreloadGlobals.webFrame.setZoomLevel(data.zoomLevel);\n\t\t}\n\n\t\t// restore parts if possible (we might not always store layout info)\n\t\tif (data?.layoutInfo) {\n\t\t\tconst { layoutInfo, colorInfo } = data;\n\n\t\t\tconst splash = document.createElement('div');\n\t\t\tsplash.id = 'monaco-parts-splash';\n\t\t\tsplash.className = baseTheme ?? 'vs-dark';\n\n\t\t\tif (layoutInfo.windowBorder && colorInfo.windowBorder) {\n\t\t\t\tconst borderElement = document.createElement('div');\n\t\t\t\tborderElement.style.position = 'absolute';\n\t\t\t\tborderElement.style.width = 'calc(100vw - 2px)';\n\t\t\t\tborderElement.style.height = 'calc(100vh - 2px)';\n\t\t\t\tborderElement.style.zIndex = '1'; // allow border above other elements\n\t\t\t\tborderElement.style.border = `1px solid var(--window-border-color)`;\n\t\t\t\tborderElement.style.setProperty('--window-border-color', colorInfo.windowBorder);\n\n\t\t\t\tif (layoutInfo.windowBorderRadius) {\n\t\t\t\t\tborderElement.style.borderRadius = layoutInfo.windowBorderRadius;\n\t\t\t\t}\n\n\t\t\t\tsplash.appendChild(borderElement);\n\t\t\t}\n\n\t\t\tif (layoutInfo.auxiliaryBarWidth === Number.MAX_SAFE_INTEGER) {\n\t\t\t\t// if auxiliary bar is maximized, it goes as wide as the\n\t\t\t\t// window width but leaving room for activity bar\n\t\t\t\tlayoutInfo.auxiliaryBarWidth = window.innerWidth - layoutInfo.activityBarWidth;\n\t\t\t} else {\n\t\t\t\t// otherwise adjust for other parts sizes if not maximized\n\t\t\t\tlayoutInfo.auxiliaryBarWidth = Math.min(layoutInfo.auxiliaryBarWidth, window.innerWidth - (layoutInfo.activityBarWidth + layoutInfo.editorPartMinWidth + layoutInfo.sideBarWidth));\n\t\t\t}\n\t\t\tlayoutInfo.sideBarWidth = Math.min(layoutInfo.sideBarWidth, window.innerWidth - (layoutInfo.activityBarWidth + layoutInfo.editorPartMinWidth + layoutInfo.auxiliaryBarWidth));\n\n\t\t\t// part: title\n\t\t\tif (layoutInfo.titleBarHeight > 0) {\n\t\t\t\tconst titleDiv = document.createElement('div');\n\t\t\t\ttitleDiv.style.position = 'absolute';\n\t\t\t\ttitleDiv.style.width = '100%';\n\t\t\t\ttitleDiv.style.height = `${layoutInfo.titleBarHeight}px`;\n\t\t\t\ttitleDiv.style.left = '0';\n\t\t\t\ttitleDiv.style.top = '0';\n\t\t\t\ttitleDiv.style.backgroundColor = `${colorInfo.titleBarBackground}`;\n\t\t\t\t(titleDiv.style as any)['-webkit-app-region'] = 'drag';\n\t\t\t\tsplash.appendChild(titleDiv);\n\n\t\t\t\tif (colorInfo.titleBarBorder) {\n\t\t\t\t\tconst titleBorder = document.createElement('div');\n\t\t\t\t\ttitleBorder.style.position = 'absolute';\n\t\t\t\t\ttitleBorder.style.width = '100%';\n\t\t\t\t\ttitleBorder.style.height = '1px';\n\t\t\t\t\ttitleBorder.style.left = '0';\n\t\t\t\t\ttitleBorder.style.bottom = '0';\n\t\t\t\t\ttitleBorder.style.borderBottom = `1px solid ${colorInfo.titleBarBorder}`;\n\t\t\t\t\ttitleDiv.appendChild(titleBorder);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// part: activity bar\n\t\t\tif (layoutInfo.activityBarWidth > 0) {\n\t\t\t\tconst activityDiv = document.createElement('div');\n\t\t\t\tactivityDiv.style.position = 'absolute';\n\t\t\t\tactivityDiv.style.width = `${layoutInfo.activityBarWidth}px`;\n\t\t\t\tactivityDiv.style.height = `calc(100% - ${layoutInfo.titleBarHeight + layoutInfo.statusBarHeight}px)`;\n\t\t\t\tactivityDiv.style.top = `${layoutInfo.titleBarHeight}px`;\n\t\t\t\tif (layoutInfo.sideBarSide === 'left') {\n\t\t\t\t\tactivityDiv.style.left = '0';\n\t\t\t\t} else {\n\t\t\t\t\tactivityDiv.style.right = '0';\n\t\t\t\t}\n\t\t\t\tactivityDiv.style.backgroundColor = `${colorInfo.activityBarBackground}`;\n\t\t\t\tsplash.appendChild(activityDiv);\n\n\t\t\t\tif (colorInfo.activityBarBorder) {\n\t\t\t\t\tconst activityBorderDiv = document.createElement('div');\n\t\t\t\t\tactivityBorderDiv.style.position = 'absolute';\n\t\t\t\t\tactivityBorderDiv.style.width = '1px';\n\t\t\t\t\tactivityBorderDiv.style.height = '100%';\n\t\t\t\t\tactivityBorderDiv.style.top = '0';\n\t\t\t\t\tif (layoutInfo.sideBarSide === 'left') {\n\t\t\t\t\t\tactivityBorderDiv.style.right = '0';\n\t\t\t\t\t\tactivityBorderDiv.style.borderRight = `1px solid ${colorInfo.activityBarBorder}`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tactivityBorderDiv.style.left = '0';\n\t\t\t\t\t\tactivityBorderDiv.style.borderLeft = `1px solid ${colorInfo.activityBarBorder}`;\n\t\t\t\t\t}\n\t\t\t\t\tactivityDiv.appendChild(activityBorderDiv);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// part: side bar\n\t\t\tif (layoutInfo.sideBarWidth > 0) {\n\t\t\t\tconst sideDiv = document.createElement('div');\n\t\t\t\tsideDiv.style.position = 'absolute';\n\t\t\t\tsideDiv.style.width = `${layoutInfo.sideBarWidth}px`;\n\t\t\t\tsideDiv.style.height = `calc(100% - ${layoutInfo.titleBarHeight + layoutInfo.statusBarHeight}px)`;\n\t\t\t\tsideDiv.style.top = `${layoutInfo.titleBarHeight}px`;\n\t\t\t\tif (layoutInfo.sideBarSide === 'left') {\n\t\t\t\t\tsideDiv.style.left = `${layoutInfo.activityBarWidth}px`;\n\t\t\t\t} else {\n\t\t\t\t\tsideDiv.style.right = `${layoutInfo.activityBarWidth}px`;\n\t\t\t\t}\n\t\t\t\tsideDiv.style.backgroundColor = `${colorInfo.sideBarBackground}`;\n\t\t\t\tsplash.appendChild(sideDiv);\n\n\t\t\t\tif (colorInfo.sideBarBorder) {\n\t\t\t\t\tconst sideBorderDiv = document.createElement('div');\n\t\t\t\t\tsideBorderDiv.style.position = 'absolute';\n\t\t\t\t\tsideBorderDiv.style.width = '1px';\n\t\t\t\t\tsideBorderDiv.style.height = '100%';\n\t\t\t\t\tsideBorderDiv.style.top = '0';\n\t\t\t\t\tsideBorderDiv.style.right = '0';\n\t\t\t\t\tif (layoutInfo.sideBarSide === 'left') {\n\t\t\t\t\t\tsideBorderDiv.style.borderRight = `1px solid ${colorInfo.sideBarBorder}`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tsideBorderDiv.style.left = '0';\n\t\t\t\t\t\tsideBorderDiv.style.borderLeft = `1px solid ${colorInfo.sideBarBorder}`;\n\t\t\t\t\t}\n\t\t\t\t\tsideDiv.appendChild(sideBorderDiv);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// part: auxiliary sidebar\n\t\t\tif (layoutInfo.auxiliaryBarWidth > 0) {\n\t\t\t\tconst auxSideDiv = document.createElement('div');\n\t\t\t\tauxSideDiv.style.position = 'absolute';\n\t\t\t\tauxSideDiv.style.width = `${layoutInfo.auxiliaryBarWidth}px`;\n\t\t\t\tauxSideDiv.style.height = `calc(100% - ${layoutInfo.titleBarHeight + layoutInfo.statusBarHeight}px)`;\n\t\t\t\tauxSideDiv.style.top = `${layoutInfo.titleBarHeight}px`;\n\t\t\t\tif (layoutInfo.sideBarSide === 'left') {\n\t\t\t\t\tauxSideDiv.style.right = '0';\n\t\t\t\t} else {\n\t\t\t\t\tauxSideDiv.style.left = '0';\n\t\t\t\t}\n\t\t\t\tauxSideDiv.style.backgroundColor = `${colorInfo.sideBarBackground}`;\n\t\t\t\tsplash.appendChild(auxSideDiv);\n\n\t\t\t\tif (colorInfo.sideBarBorder) {\n\t\t\t\t\tconst auxSideBorderDiv = document.createElement('div');\n\t\t\t\t\tauxSideBorderDiv.style.position = 'absolute';\n\t\t\t\t\tauxSideBorderDiv.style.width = '1px';\n\t\t\t\t\tauxSideBorderDiv.style.height = '100%';\n\t\t\t\t\tauxSideBorderDiv.style.top = '0';\n\t\t\t\t\tif (layoutInfo.sideBarSide === 'left') {\n\t\t\t\t\t\tauxSideBorderDiv.style.left = '0';\n\t\t\t\t\t\tauxSideBorderDiv.style.borderLeft = `1px solid ${colorInfo.sideBarBorder}`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tauxSideBorderDiv.style.right = '0';\n\t\t\t\t\t\tauxSideBorderDiv.style.borderRight = `1px solid ${colorInfo.sideBarBorder}`;\n\t\t\t\t\t}\n\t\t\t\t\tauxSideDiv.appendChild(auxSideBorderDiv);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// part: statusbar\n\t\t\tif (layoutInfo.statusBarHeight > 0) {\n\t\t\t\tconst statusDiv = document.createElement('div');\n\t\t\t\tstatusDiv.style.position = 'absolute';\n\t\t\t\tstatusDiv.style.width = '100%';\n\t\t\t\tstatusDiv.style.height = `${layoutInfo.statusBarHeight}px`;\n\t\t\t\tstatusDiv.style.bottom = '0';\n\t\t\t\tstatusDiv.style.left = '0';\n\t\t\t\tif (configuration.workspace && colorInfo.statusBarBackground) {\n\t\t\t\t\tstatusDiv.style.backgroundColor = colorInfo.statusBarBackground;\n\t\t\t\t} else if (!configuration.workspace && colorInfo.statusBarNoFolderBackground) {\n\t\t\t\t\tstatusDiv.style.backgroundColor = colorInfo.statusBarNoFolderBackground;\n\t\t\t\t}\n\t\t\t\tsplash.appendChild(statusDiv);\n\n\t\t\t\tif (colorInfo.statusBarBorder) {\n\t\t\t\t\tconst statusBorderDiv = document.createElement('div');\n\t\t\t\t\tstatusBorderDiv.style.position = 'absolute';\n\t\t\t\t\tstatusBorderDiv.style.width = '100%';\n\t\t\t\t\tstatusBorderDiv.style.height = '1px';\n\t\t\t\t\tstatusBorderDiv.style.top = '0';\n\t\t\t\t\tstatusBorderDiv.style.borderTop = `1px solid ${colorInfo.statusBarBorder}`;\n\t\t\t\t\tstatusDiv.appendChild(statusBorderDiv);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\twindow.document.body.appendChild(splash);\n\t\t}\n\n\t\tperformance.mark('code/didShowPartsSplash');\n\t}\n\n\t//#endregion\n\n\t//#region Window Helpers\n\n\tasync function load<M, T extends ISandboxConfiguration>(esModule: string, options: ILoadOptions<T>): Promise<ILoadResult<M, T>> {\n\n\t\t// Window Configuration from Preload Script\n\t\tconst configuration = await resolveWindowConfiguration<T>();\n\n\t\t// Signal before import()\n\t\toptions?.beforeImport?.(configuration);\n\n\t\t// Developer settings\n\t\tconst { enableDeveloperKeybindings, removeDeveloperKeybindingsAfterLoad, developerDeveloperKeybindingsDisposable, forceDisableShowDevtoolsOnError } = setupDeveloperKeybindings(configuration, options);\n\n\t\t// NLS\n\t\tsetupNLS<T>(configuration);\n\n\t\t// Compute base URL and set as global\n\t\tconst baseUrl = new URL(`${fileUriFromPath(configuration.appRoot, { isWindows: safeProcess.platform === 'win32', scheme: 'vscode-file', fallbackAuthority: 'vscode-app' })}/out/`);\n\t\tglobalThis._VSCODE_FILE_ROOT = baseUrl.toString();\n\n\t\t// Dev only: CSS import map tricks\n\t\tsetupCSSImportMaps<T>(configuration, baseUrl);\n\n\t\t// ESM Import\n\t\ttry {\n\t\t\tconst result = await import(new URL(`${esModule}.js`, baseUrl).href);\n\n\t\t\tif (developerDeveloperKeybindingsDisposable && removeDeveloperKeybindingsAfterLoad) {\n\t\t\t\tdeveloperDeveloperKeybindingsDisposable();\n\t\t\t}\n\n\t\t\treturn { result, configuration };\n\t\t} catch (error) {\n\t\t\tonUnexpectedError(error, enableDeveloperKeybindings && !forceDisableShowDevtoolsOnError);\n\n\t\t\tthrow error;\n\t\t}\n\t}\n\n\tasync function resolveWindowConfiguration<T extends ISandboxConfiguration>() {\n\t\tconst timeout = setTimeout(() => { console.error(`[resolve window config] Could not resolve window configuration within 10 seconds, but will continue to wait...`); }, 10000);\n\t\tperformance.mark('code/willWaitForWindowConfig');\n\n\t\tconst configuration = await preloadGlobals.context.resolveConfiguration() as T;\n\t\tperformance.mark('code/didWaitForWindowConfig');\n\n\t\tclearTimeout(timeout);\n\n\t\treturn configuration;\n\t}\n\n\tfunction setupDeveloperKeybindings<T extends ISandboxConfiguration>(configuration: T, options: ILoadOptions<T>) {\n\t\tconst {\n\t\t\tforceEnableDeveloperKeybindings,\n\t\t\tdisallowReloadKeybinding,\n\t\t\tremoveDeveloperKeybindingsAfterLoad,\n\t\t\tforceDisableShowDevtoolsOnError\n\t\t} = typeof options?.configureDeveloperSettings === 'function' ? options.configureDeveloperSettings(configuration) : {\n\t\t\tforceEnableDeveloperKeybindings: false,\n\t\t\tdisallowReloadKeybinding: false,\n\t\t\tremoveDeveloperKeybindingsAfterLoad: false,\n\t\t\tforceDisableShowDevtoolsOnError: false\n\t\t};\n\n\t\tconst isDev = !!safeProcess.env['VSCODE_DEV'];\n\t\tconst enableDeveloperKeybindings = Boolean(isDev || forceEnableDeveloperKeybindings);\n\t\tlet developerDeveloperKeybindingsDisposable: Function | undefined = undefined;\n\t\tif (enableDeveloperKeybindings) {\n\t\t\tdeveloperDeveloperKeybindingsDisposable = registerDeveloperKeybindings(disallowReloadKeybinding);\n\t\t}\n\n\t\treturn {\n\t\t\tenableDeveloperKeybindings,\n\t\t\tremoveDeveloperKeybindingsAfterLoad,\n\t\t\tdeveloperDeveloperKeybindingsDisposable,\n\t\t\tforceDisableShowDevtoolsOnError\n\t\t};\n\t}\n\n\tfunction registerDeveloperKeybindings(disallowReloadKeybinding: boolean | undefined): Function {\n\t\tconst ipcRenderer = preloadGlobals.ipcRenderer;\n\n\t\tconst extractKey =\n\t\t\tfunction (e: KeyboardEvent) {\n\t\t\t\treturn [\n\t\t\t\t\te.ctrlKey ? 'ctrl-' : '',\n\t\t\t\t\te.metaKey ? 'meta-' : '',\n\t\t\t\t\te.altKey ? 'alt-' : '',\n\t\t\t\t\te.shiftKey ? 'shift-' : '',\n\t\t\t\t\te.keyCode\n\t\t\t\t].join('');\n\t\t\t};\n\n\t\t// Devtools & reload support\n\t\tconst TOGGLE_DEV_TOOLS_KB = (safeProcess.platform === 'darwin' ? 'meta-alt-73' : 'ctrl-shift-73'); // mac: Cmd-Alt-I, rest: Ctrl-Shift-I\n\t\tconst TOGGLE_DEV_TOOLS_KB_ALT = '123'; // F12\n\t\tconst RELOAD_KB = (safeProcess.platform === 'darwin' ? 'meta-82' : 'ctrl-82'); // mac: Cmd-R, rest: Ctrl-R\n\n\t\tlet listener: ((e: KeyboardEvent) => void) | undefined = function (e) {\n\t\t\tconst key = extractKey(e);\n\t\t\tif (key === TOGGLE_DEV_TOOLS_KB || key === TOGGLE_DEV_TOOLS_KB_ALT) {\n\t\t\t\tipcRenderer.send('vscode:toggleDevTools');\n\t\t\t} else if (key === RELOAD_KB && !disallowReloadKeybinding) {\n\t\t\t\tipcRenderer.send('vscode:reloadWindow');\n\t\t\t}\n\t\t};\n\n\t\twindow.addEventListener('keydown', listener);\n\n\t\treturn function () {\n\t\t\tif (listener) {\n\t\t\t\twindow.removeEventListener('keydown', listener);\n\t\t\t\tlistener = undefined;\n\t\t\t}\n\t\t};\n\t}\n\n\tfunction setupNLS<T extends ISandboxConfiguration>(configuration: T): void {\n\t\tglobalThis._VSCODE_NLS_MESSAGES = configuration.nls.messages;\n\t\tglobalThis._VSCODE_NLS_LANGUAGE = configuration.nls.language;\n\n\t\tlet language = configuration.nls.language || 'en';\n\t\tif (language === 'zh-tw') {\n\t\t\tlanguage = 'zh-Hant';\n\t\t} else if (language === 'zh-cn') {\n\t\t\tlanguage = 'zh-Hans';\n\t\t}\n\n\t\twindow.document.documentElement.setAttribute('lang', language);\n\t}\n\n\tfunction onUnexpectedError(error: string | Error, showDevtoolsOnError: boolean): void {\n\t\tif (showDevtoolsOnError) {\n\t\t\tconst ipcRenderer = preloadGlobals.ipcRenderer;\n\t\t\tipcRenderer.send('vscode:openDevTools');\n\t\t}\n\n\t\tconsole.error(`[uncaught exception]: ${error}`);\n\n\t\tif (error && typeof error !== 'string' && error.stack) {\n\t\t\tconsole.error(error.stack);\n\t\t}\n\t}\n\n\tfunction fileUriFromPath(path: string, config: { isWindows?: boolean; scheme?: string; fallbackAuthority?: string }): string {\n\n\t\t// Since we are building a URI, we normalize any backslash\n\t\t// to slashes and we ensure that the path begins with a '/'.\n\t\tlet pathName = path.replace(/\\\\/g, '/');\n\t\tif (pathName.length > 0 && pathName.charAt(0) !== '/') {\n\t\t\tpathName = `/${pathName}`;\n\t\t}\n\n\t\tlet uri: string;\n\n\t\t// Windows: in order to support UNC paths (which start with '//')\n\t\t// that have their own authority, we do not use the provided authority\n\t\t// but rather preserve it.\n\t\tif (config.isWindows && pathName.startsWith('//')) {\n\t\t\turi = encodeURI(`${config.scheme || 'file'}:${pathName}`);\n\t\t}\n\n\t\t// Otherwise we optionally add the provided authority if specified\n\t\telse {\n\t\t\turi = encodeURI(`${config.scheme || 'file'}://${config.fallbackAuthority || ''}${pathName}`);\n\t\t}\n\n\t\treturn uri.replace(/#/g, '%23');\n\t}\n\n\tfunction setupCSSImportMaps<T extends ISandboxConfiguration>(configuration: T, baseUrl: URL) {\n\n\t\t// DEV ---------------------------------------------------------------------------------------\n\t\t// DEV: This is for development and enables loading CSS via import-statements via import-maps.\n\t\t// DEV: For each CSS modules that we have we defined an entry in the import map that maps to\n\t\t// DEV: a blob URL that loads the CSS via a dynamic @import-rule.\n\t\t// DEV ---------------------------------------------------------------------------------------\n\n\t\tif (Array.isArray(configuration.cssModules) && configuration.cssModules.length > 0) {\n\t\t\tperformance.mark('code/willAddCssLoader');\n\n\t\t\tglobalThis._VSCODE_CSS_LOAD = function (url) {\n\t\t\t\tconst link = document.createElement('link');\n\t\t\t\tlink.setAttribute('rel', 'stylesheet');\n\t\t\t\tlink.setAttribute('type', 'text/css');\n\t\t\t\tlink.setAttribute('href', url);\n\n\t\t\t\twindow.document.head.appendChild(link);\n\t\t\t};\n\n\t\t\tconst importMap: { imports: Record<string, string> } = { imports: {} };\n\t\t\tfor (const cssModule of configuration.cssModules) {\n\t\t\t\tconst cssUrl = new URL(cssModule, baseUrl).href;\n\t\t\t\tconst jsSrc = `globalThis._VSCODE_CSS_LOAD('${cssUrl}');\\n`;\n\t\t\t\tconst blob = new Blob([jsSrc], { type: 'application/javascript' });\n\t\t\t\timportMap.imports[cssUrl] = URL.createObjectURL(blob);\n\t\t\t}\n\n\t\t\tconst ttp = window.trustedTypes?.createPolicy('vscode-bootstrapImportMap', { createScript(value) { return value; }, });\n\t\t\tconst importMapSrc = JSON.stringify(importMap, undefined, 2);\n\t\t\tconst importMapScript = document.createElement('script');\n\t\t\timportMapScript.type = 'importmap';\n\t\t\timportMapScript.setAttribute('nonce', '0c6a828f1297');\n\t\t\t// @ts-ignore\n\t\t\timportMapScript.textContent = ttp?.createScript(importMapSrc) ?? importMapSrc;\n\t\t\twindow.document.head.appendChild(importMapScript);\n\n\t\t\tperformance.mark('code/didAddCssLoader');\n\t\t}\n\t}\n\n\t//#endregion\n\n\tconst { result, configuration } = await load<IDesktopMain, INativeWindowConfiguration>('vs/workbench/workbench.desktop.main',\n\t\t{\n\t\t\tconfigureDeveloperSettings: function (windowConfig) {\n\t\t\t\treturn {\n\t\t\t\t\t// disable automated devtools opening on error when running extension tests\n\t\t\t\t\t// as this can lead to nondeterministic test execution (devtools steals focus)\n\t\t\t\t\tforceDisableShowDevtoolsOnError: typeof windowConfig.extensionTestsPath === 'string' || windowConfig['enable-smoke-test-driver'] === true,\n\t\t\t\t\t// enable devtools keybindings in extension development window\n\t\t\t\t\tforceEnableDeveloperKeybindings: Array.isArray(windowConfig.extensionDevelopmentPath) && windowConfig.extensionDevelopmentPath.length > 0,\n\t\t\t\t\tremoveDeveloperKeybindingsAfterLoad: true\n\t\t\t\t};\n\t\t\t},\n\t\t\tbeforeImport: function (windowConfig) {\n\n\t\t\t\t// Show our splash as early as possible\n\t\t\t\tshowSplash(windowConfig);\n\n\t\t\t\t// Code windows have a `vscodeWindowId` property to identify them\n\t\t\t\tObject.defineProperty(window, 'vscodeWindowId', {\n\t\t\t\t\tget: () => windowConfig.windowId\n\t\t\t\t});\n\n\t\t\t\t// It looks like browsers only lazily enable\n\t\t\t\t// the <canvas> element when needed. Since we\n\t\t\t\t// leverage canvas elements in our code in many\n\t\t\t\t// locations, we try to help the browser to\n\t\t\t\t// initialize canvas when it is idle, right\n\t\t\t\t// before we wait for the scripts to be loaded.\n\t\t\t\twindow.requestIdleCallback(() => {\n\t\t\t\t\tconst canvas = document.createElement('canvas');\n\t\t\t\t\tconst context = canvas.getContext('2d');\n\t\t\t\t\tcontext?.clearRect(0, 0, canvas.width, canvas.height);\n\t\t\t\t\tcanvas.remove();\n\t\t\t\t}, { timeout: 50 });\n\n\t\t\t\t// Track import() perf\n\t\t\t\tperformance.mark('code/willLoadWorkbenchMain');\n\t\t\t}\n\t\t}\n\t);\n\n\t// Mark start of workbench\n\tperformance.mark('code/didLoadWorkbenchMain');\n\n\t// Load workbench\n\tresult.main(configuration);\n}());\n"], "mappings": ";;6DAOC,gBAAK,CAGL,YAAY,KAAK,uBAAuB,EASxC,MAAMA,EAA6C,OAAe,OAC5DC,EAAcD,EAAe,QAInC,SAASE,EAAWC,EAAyC,CAC5D,YAAY,KAAK,0BAA0B,EAE3C,IAAIC,EAAOD,EAAc,YACrBC,IACCD,EAAc,wBAA0BA,EAAc,YAAY,cAChEA,EAAc,YAAY,MAAQC,EAAK,YAAc,YAAgB,CAACD,EAAc,YAAY,MAAQC,EAAK,YAAc,cAC/HA,EAAO,QAEED,EAAc,wBACnBA,EAAc,YAAY,MAAQC,EAAK,YAAc,WAAe,CAACD,EAAc,YAAY,MAAQC,EAAK,YAAc,QAC9HA,EAAO,SAMNA,GAAQD,EAAc,2BACzBC,EAAK,WAAa,QAInB,IAAIC,EACAC,EACAC,EACAH,GACHC,EAAYD,EAAK,UACjBE,EAAkBF,EAAK,UAAU,iBACjCG,EAAkBH,EAAK,UAAU,YACvBD,EAAc,wBAA0BA,EAAc,YAAY,aACxEA,EAAc,YAAY,MAC7BE,EAAY,WACZC,EAAkB,UAClBC,EAAkB,YAElBF,EAAY,WACZC,EAAkB,UAClBC,EAAkB,WAETJ,EAAc,wBACpBA,EAAc,YAAY,MAC7BE,EAAY,UACZC,EAAkB,UAClBC,EAAkB,YAElBF,EAAY,KACZC,EAAkB,UAClBC,EAAkB,YAIpB,MAAMC,EAAQ,SAAS,cAAc,OAAO,EAW5C,GAVAA,EAAM,UAAY,qBAClB,OAAO,SAAS,KAAK,YAAYA,CAAK,EACtCA,EAAM,YAAc,4BAA4BF,CAAe,YAAYC,CAAe,6BAGtF,OAAOH,GAAM,WAAc,UAAY,OAAOJ,GAAgB,UAAU,cAAiB,YAC5FA,EAAe,SAAS,aAAaI,EAAK,SAAS,EAIhDA,GAAM,WAAY,CACrB,KAAM,CAAE,WAAAK,EAAY,UAAAC,CAAS,EAAKN,EAE5BO,EAAS,SAAS,cAAc,KAAK,EAI3C,GAHAA,EAAO,GAAK,sBACZA,EAAO,UAAYN,GAAa,UAE5BI,EAAW,cAAgBC,EAAU,aAAc,CACtD,MAAME,EAAgB,SAAS,cAAc,KAAK,EAClDA,EAAc,MAAM,SAAW,WAC/BA,EAAc,MAAM,MAAQ,oBAC5BA,EAAc,MAAM,OAAS,oBAC7BA,EAAc,MAAM,OAAS,IAC7BA,EAAc,MAAM,OAAS,uCAC7BA,EAAc,MAAM,YAAY,wBAAyBF,EAAU,YAAY,EAE3ED,EAAW,qBACdG,EAAc,MAAM,aAAeH,EAAW,oBAG/CE,EAAO,YAAYC,CAAa,CACjC,CAaA,GAXIH,EAAW,oBAAsB,OAAO,iBAG3CA,EAAW,kBAAoB,OAAO,WAAaA,EAAW,iBAG9DA,EAAW,kBAAoB,KAAK,IAAIA,EAAW,kBAAmB,OAAO,YAAcA,EAAW,iBAAmBA,EAAW,mBAAqBA,EAAW,aAAa,EAElLA,EAAW,aAAe,KAAK,IAAIA,EAAW,aAAc,OAAO,YAAcA,EAAW,iBAAmBA,EAAW,mBAAqBA,EAAW,kBAAkB,EAGxKA,EAAW,eAAiB,EAAG,CAClC,MAAMI,EAAW,SAAS,cAAc,KAAK,EAU7C,GATAA,EAAS,MAAM,SAAW,WAC1BA,EAAS,MAAM,MAAQ,OACvBA,EAAS,MAAM,OAAS,GAAGJ,EAAW,cAAc,KACpDI,EAAS,MAAM,KAAO,IACtBA,EAAS,MAAM,IAAM,IACrBA,EAAS,MAAM,gBAAkB,GAAGH,EAAU,kBAAkB,GAC/DG,EAAS,MAAc,oBAAoB,EAAI,OAChDF,EAAO,YAAYE,CAAQ,EAEvBH,EAAU,eAAgB,CAC7B,MAAMI,EAAc,SAAS,cAAc,KAAK,EAChDA,EAAY,MAAM,SAAW,WAC7BA,EAAY,MAAM,MAAQ,OAC1BA,EAAY,MAAM,OAAS,MAC3BA,EAAY,MAAM,KAAO,IACzBA,EAAY,MAAM,OAAS,IAC3BA,EAAY,MAAM,aAAe,aAAaJ,EAAU,cAAc,GACtEG,EAAS,YAAYC,CAAW,CACjC,CACD,CAGA,GAAIL,EAAW,iBAAmB,EAAG,CACpC,MAAMM,EAAc,SAAS,cAAc,KAAK,EAahD,GAZAA,EAAY,MAAM,SAAW,WAC7BA,EAAY,MAAM,MAAQ,GAAGN,EAAW,gBAAgB,KACxDM,EAAY,MAAM,OAAS,eAAeN,EAAW,eAAiBA,EAAW,eAAe,MAChGM,EAAY,MAAM,IAAM,GAAGN,EAAW,cAAc,KAChDA,EAAW,cAAgB,OAC9BM,EAAY,MAAM,KAAO,IAEzBA,EAAY,MAAM,MAAQ,IAE3BA,EAAY,MAAM,gBAAkB,GAAGL,EAAU,qBAAqB,GACtEC,EAAO,YAAYI,CAAW,EAE1BL,EAAU,kBAAmB,CAChC,MAAMM,EAAoB,SAAS,cAAc,KAAK,EACtDA,EAAkB,MAAM,SAAW,WACnCA,EAAkB,MAAM,MAAQ,MAChCA,EAAkB,MAAM,OAAS,OACjCA,EAAkB,MAAM,IAAM,IAC1BP,EAAW,cAAgB,QAC9BO,EAAkB,MAAM,MAAQ,IAChCA,EAAkB,MAAM,YAAc,aAAaN,EAAU,iBAAiB,KAE9EM,EAAkB,MAAM,KAAO,IAC/BA,EAAkB,MAAM,WAAa,aAAaN,EAAU,iBAAiB,IAE9EK,EAAY,YAAYC,CAAiB,CAC1C,CACD,CAGA,GAAIP,EAAW,aAAe,EAAG,CAChC,MAAMQ,EAAU,SAAS,cAAc,KAAK,EAa5C,GAZAA,EAAQ,MAAM,SAAW,WACzBA,EAAQ,MAAM,MAAQ,GAAGR,EAAW,YAAY,KAChDQ,EAAQ,MAAM,OAAS,eAAeR,EAAW,eAAiBA,EAAW,eAAe,MAC5FQ,EAAQ,MAAM,IAAM,GAAGR,EAAW,cAAc,KAC5CA,EAAW,cAAgB,OAC9BQ,EAAQ,MAAM,KAAO,GAAGR,EAAW,gBAAgB,KAEnDQ,EAAQ,MAAM,MAAQ,GAAGR,EAAW,gBAAgB,KAErDQ,EAAQ,MAAM,gBAAkB,GAAGP,EAAU,iBAAiB,GAC9DC,EAAO,YAAYM,CAAO,EAEtBP,EAAU,cAAe,CAC5B,MAAMQ,EAAgB,SAAS,cAAc,KAAK,EAClDA,EAAc,MAAM,SAAW,WAC/BA,EAAc,MAAM,MAAQ,MAC5BA,EAAc,MAAM,OAAS,OAC7BA,EAAc,MAAM,IAAM,IAC1BA,EAAc,MAAM,MAAQ,IACxBT,EAAW,cAAgB,OAC9BS,EAAc,MAAM,YAAc,aAAaR,EAAU,aAAa,IAEtEQ,EAAc,MAAM,KAAO,IAC3BA,EAAc,MAAM,WAAa,aAAaR,EAAU,aAAa,IAEtEO,EAAQ,YAAYC,CAAa,CAClC,CACD,CAGA,GAAIT,EAAW,kBAAoB,EAAG,CACrC,MAAMU,EAAa,SAAS,cAAc,KAAK,EAa/C,GAZAA,EAAW,MAAM,SAAW,WAC5BA,EAAW,MAAM,MAAQ,GAAGV,EAAW,iBAAiB,KACxDU,EAAW,MAAM,OAAS,eAAeV,EAAW,eAAiBA,EAAW,eAAe,MAC/FU,EAAW,MAAM,IAAM,GAAGV,EAAW,cAAc,KAC/CA,EAAW,cAAgB,OAC9BU,EAAW,MAAM,MAAQ,IAEzBA,EAAW,MAAM,KAAO,IAEzBA,EAAW,MAAM,gBAAkB,GAAGT,EAAU,iBAAiB,GACjEC,EAAO,YAAYQ,CAAU,EAEzBT,EAAU,cAAe,CAC5B,MAAMU,EAAmB,SAAS,cAAc,KAAK,EACrDA,EAAiB,MAAM,SAAW,WAClCA,EAAiB,MAAM,MAAQ,MAC/BA,EAAiB,MAAM,OAAS,OAChCA,EAAiB,MAAM,IAAM,IACzBX,EAAW,cAAgB,QAC9BW,EAAiB,MAAM,KAAO,IAC9BA,EAAiB,MAAM,WAAa,aAAaV,EAAU,aAAa,KAExEU,EAAiB,MAAM,MAAQ,IAC/BA,EAAiB,MAAM,YAAc,aAAaV,EAAU,aAAa,IAE1ES,EAAW,YAAYC,CAAgB,CACxC,CACD,CAGA,GAAIX,EAAW,gBAAkB,EAAG,CACnC,MAAMY,EAAY,SAAS,cAAc,KAAK,EAa9C,GAZAA,EAAU,MAAM,SAAW,WAC3BA,EAAU,MAAM,MAAQ,OACxBA,EAAU,MAAM,OAAS,GAAGZ,EAAW,eAAe,KACtDY,EAAU,MAAM,OAAS,IACzBA,EAAU,MAAM,KAAO,IACnBlB,EAAc,WAAaO,EAAU,oBACxCW,EAAU,MAAM,gBAAkBX,EAAU,oBAClC,CAACP,EAAc,WAAaO,EAAU,8BAChDW,EAAU,MAAM,gBAAkBX,EAAU,6BAE7CC,EAAO,YAAYU,CAAS,EAExBX,EAAU,gBAAiB,CAC9B,MAAMY,EAAkB,SAAS,cAAc,KAAK,EACpDA,EAAgB,MAAM,SAAW,WACjCA,EAAgB,MAAM,MAAQ,OAC9BA,EAAgB,MAAM,OAAS,MAC/BA,EAAgB,MAAM,IAAM,IAC5BA,EAAgB,MAAM,UAAY,aAAaZ,EAAU,eAAe,GACxEW,EAAU,YAAYC,CAAe,CACtC,CACD,CAEA,OAAO,SAAS,KAAK,YAAYX,CAAM,CACxC,CAEA,YAAY,KAAK,yBAAyB,CAC3C,CAMA,eAAeY,EAAyCC,EAAkBC,EAAwB,CAGjG,MAAMtB,EAAgB,MAAMuB,EAA0B,EAGtDD,GAAS,eAAetB,CAAa,EAGrC,KAAM,CAAE,2BAAAwB,EAA4B,oCAAAC,EAAqC,wCAAAC,EAAyC,gCAAAC,CAA+B,EAAKC,EAA0B5B,EAAesB,CAAO,EAGtMO,EAAY7B,CAAa,EAGzB,MAAM8B,EAAU,IAAI,IAAI,GAAGC,EAAgB/B,EAAc,QAAS,CAAE,UAAWF,EAAY,WAAa,QAAS,OAAQ,cAAe,kBAAmB,YAAY,CAAE,CAAC,OAAO,EACjL,WAAW,kBAAoBgC,EAAQ,SAAQ,EAG/CE,EAAsBhC,EAAe8B,CAAO,EAG5C,GAAI,CACH,MAAMG,EAAS,MAAM,OAAO,IAAI,IAAI,GAAGZ,CAAQ,MAAOS,CAAO,EAAE,MAE/D,OAAIJ,GAA2CD,GAC9CC,EAAuC,EAGjC,CAAE,OAAAO,EAAQ,cAAAjC,CAAa,CAC/B,OAASkC,EAAO,CACf,MAAAC,EAAkBD,EAAOV,GAA8B,CAACG,CAA+B,EAEjFO,CACP,CACD,CAEA,eAAeX,GAA0B,CACxC,MAAMa,EAAU,WAAW,IAAK,CAAG,QAAQ,MAAM,gHAAgH,CAAG,EAAG,GAAK,EAC5K,YAAY,KAAK,8BAA8B,EAE/C,MAAMpC,EAAgB,MAAMH,EAAe,QAAQ,qBAAoB,EACvE,mBAAY,KAAK,6BAA6B,EAE9C,aAAauC,CAAO,EAEbpC,CACR,CAEA,SAAS4B,EAA2D5B,EAAkBsB,EAAwB,CAC7G,KAAM,CACL,gCAAAe,EACA,yBAAAC,EACA,oCAAAb,EACA,gCAAAE,CAA+B,EAC5B,OAAOL,GAAS,4BAA+B,WAAaA,EAAQ,2BAA2BtB,CAAa,EAAI,CACnH,gCAAiC,GACjC,yBAA0B,GAC1B,oCAAqC,GACrC,gCAAiC,IAI5BwB,EAA6B,GADrB,CAAC,CAAC1B,EAAY,IAAI,YACoBuC,GACpD,IAAIX,EACJ,OAAIF,IACHE,EAA0Ca,EAA6BD,CAAwB,GAGzF,CACN,2BAAAd,EACA,oCAAAC,EACA,wCAAAC,EACA,gCAAAC,EAEF,CAEA,SAASY,EAA6BD,EAA6C,CAClF,MAAME,EAAc3C,EAAe,YAE7B4C,EACL,SAAUC,EAAgB,CACzB,MAAO,CACNA,EAAE,QAAU,QAAU,GACtBA,EAAE,QAAU,QAAU,GACtBA,EAAE,OAAS,OAAS,GACpBA,EAAE,SAAW,SAAW,GACxBA,EAAE,SACD,KAAK,EAAE,CACV,EAGKC,EAAuB7C,EAAY,WAAa,SAAW,cAAgB,gBAC3E8C,EAA0B,MAC1BC,EAAa/C,EAAY,WAAa,SAAW,UAAY,UAEnE,IAAIgD,EAAqD,SAAUJ,EAAC,CACnE,MAAMK,EAAMN,EAAWC,CAAC,EACpBK,IAAQJ,GAAuBI,IAAQH,EAC1CJ,EAAY,KAAK,uBAAuB,EAC9BO,IAAQF,GAAa,CAACP,GAChCE,EAAY,KAAK,qBAAqB,CAExC,EAEA,cAAO,iBAAiB,UAAWM,CAAQ,EAEpC,UAAA,CACFA,IACH,OAAO,oBAAoB,UAAWA,CAAQ,EAC9CA,EAAW,OAEb,CACD,CAEA,SAASjB,EAA0C7B,EAAgB,CAClE,WAAW,qBAAuBA,EAAc,IAAI,SACpD,WAAW,qBAAuBA,EAAc,IAAI,SAEpD,IAAIgD,EAAWhD,EAAc,IAAI,UAAY,KACzCgD,IAAa,QAChBA,EAAW,UACDA,IAAa,UACvBA,EAAW,WAGZ,OAAO,SAAS,gBAAgB,aAAa,OAAQA,CAAQ,CAC9D,CAEA,SAASb,EAAkBD,EAAuBe,EAA4B,CACzEA,GACiBpD,EAAe,YACvB,KAAK,qBAAqB,EAGvC,QAAQ,MAAM,yBAAyBqC,CAAK,EAAE,EAE1CA,GAAS,OAAOA,GAAU,UAAYA,EAAM,OAC/C,QAAQ,MAAMA,EAAM,KAAK,CAE3B,CAEA,SAASH,EAAgBmB,EAAcC,EAA4E,CAIlH,IAAIC,EAAWF,EAAK,QAAQ,MAAO,GAAG,EAClCE,EAAS,OAAS,GAAKA,EAAS,OAAO,CAAC,IAAM,MACjDA,EAAW,IAAIA,CAAQ,IAGxB,IAAIC,EAKJ,OAAIF,EAAO,WAAaC,EAAS,WAAW,IAAI,EAC/CC,EAAM,UAAU,GAAGF,EAAO,QAAU,MAAM,IAAIC,CAAQ,EAAE,EAKxDC,EAAM,UAAU,GAAGF,EAAO,QAAU,MAAM,MAAMA,EAAO,mBAAqB,EAAE,GAAGC,CAAQ,EAAE,EAGrFC,EAAI,QAAQ,KAAM,KAAK,CAC/B,CAEA,SAASrB,EAAoDhC,EAAkB8B,EAAY,CAQ1F,GAAI,MAAM,QAAQ9B,EAAc,UAAU,GAAKA,EAAc,WAAW,OAAS,EAAG,CACnF,YAAY,KAAK,uBAAuB,EAExC,WAAW,iBAAmB,SAAUsD,EAAG,CAC1C,MAAMC,EAAO,SAAS,cAAc,MAAM,EAC1CA,EAAK,aAAa,MAAO,YAAY,EACrCA,EAAK,aAAa,OAAQ,UAAU,EACpCA,EAAK,aAAa,OAAQD,CAAG,EAE7B,OAAO,SAAS,KAAK,YAAYC,CAAI,CACtC,EAEA,MAAMC,EAAiD,CAAE,QAAS,CAAA,CAAE,EACpE,UAAWC,KAAazD,EAAc,WAAY,CACjD,MAAM0D,EAAS,IAAI,IAAID,EAAW3B,CAAO,EAAE,KACrC6B,EAAQ,gCAAgCD,CAAM;EAC9CE,EAAO,IAAI,KAAK,CAACD,CAAK,EAAG,CAAE,KAAM,wBAAwB,CAAE,EACjEH,EAAU,QAAQE,CAAM,EAAI,IAAI,gBAAgBE,CAAI,CACrD,CAEA,MAAMC,EAAM,OAAO,cAAc,aAAa,4BAA6B,CAAE,aAAaC,EAAK,CAAI,OAAOA,CAAO,CAAC,CAAG,EAC/GC,EAAe,KAAK,UAAUP,EAAW,OAAW,CAAC,EACrDQ,EAAkB,SAAS,cAAc,QAAQ,EACvDA,EAAgB,KAAO,YACvBA,EAAgB,aAAa,QAAS,cAAc,EAEpDA,EAAgB,YAAcH,GAAK,aAAaE,CAAY,GAAKA,EACjE,OAAO,SAAS,KAAK,YAAYC,CAAe,EAEhD,YAAY,KAAK,sBAAsB,CACxC,CACD,CAIA,KAAM,CAAE,OAAA/B,EAAQ,cAAAjC,CAAa,EAAK,MAAMoB,EAA+C,sCACtF,CACC,2BAA4B,SAAU6C,EAAY,CACjD,MAAO,CAGN,gCAAiC,OAAOA,EAAa,oBAAuB,UAAYA,EAAa,0BAA0B,IAAM,GAErI,gCAAiC,MAAM,QAAQA,EAAa,wBAAwB,GAAKA,EAAa,yBAAyB,OAAS,EACxI,oCAAqC,GAEvC,EACA,aAAc,SAAUA,EAAY,CAGnClE,EAAWkE,CAAY,EAGvB,OAAO,eAAe,OAAQ,iBAAkB,CAC/C,IAAK,IAAMA,EAAa,SACxB,EAQD,OAAO,oBAAoB,IAAK,CAC/B,MAAMC,EAAS,SAAS,cAAc,QAAQ,EAC9BA,EAAO,WAAW,IAAI,GAC7B,UAAU,EAAG,EAAGA,EAAO,MAAOA,EAAO,MAAM,EACpDA,EAAO,OAAM,CACd,EAAG,CAAE,QAAS,EAAE,CAAE,EAGlB,YAAY,KAAK,4BAA4B,CAC9C,EACA,EAIF,YAAY,KAAK,2BAA2B,EAG5CjC,EAAO,KAAKjC,CAAa,CAC1B,GAAC", "names": ["preloadGlobals", "safeProcess", "showSplash", "configuration", "data", "baseTheme", "shellBackground", "shellForeground", "style", "layoutInfo", "colorInfo", "splash", "borderElement", "titleDiv", "titleBorder", "activityDiv", "activityBorderDiv", "sideDiv", "sideBorderDiv", "auxSideDiv", "auxSideBorderDiv", "statusDiv", "statusBorderDiv", "load", "esModule", "options", "resolveWindowConfiguration", "enableDeveloperKeybindings", "removeDeveloperKeybindingsAfterLoad", "developerDeveloperKeybindingsDisposable", "forceDisableShowDevtoolsOnError", "setupDeveloperKeybindings", "setupNLS", "baseUrl", "fileUriFromPath", "setupCSSImportMaps", "result", "error", "onUnexpectedError", "timeout", "forceEnableDeveloperKeybindings", "disallowReloadKeybinding", "registerDeveloperKeybindings", "ip<PERSON><PERSON><PERSON><PERSON>", "extractKey", "e", "TOGGLE_DEV_TOOLS_KB", "TOGGLE_DEV_TOOLS_KB_ALT", "RELOAD_KB", "listener", "key", "language", "showDevtoolsOnError", "path", "config", "pathName", "uri", "url", "link", "importMap", "cssModule", "cssUrl", "jsSrc", "blob", "ttp", "value", "importMapSrc", "importMapScript", "windowConfig", "canvas"], "file": "workbench.js"}