2025-07-28 20:08:32.675 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-07-28 20:08:32.675 [info] Using the Electron fetcher.
2025-07-28 20:08:32.675 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-07-28 20:08:32.675 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-07-28 20:08:32.675 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-07-28 20:08:32.795 [warning] GitHub login failed
2025-07-28 20:08:32.971 [warning] GitHub login failed
2025-07-28 20:08:32.976 [error] Error: GitHubLoginFailed
    at cO._authShowWarnings (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:552:58352)
    at cO.getCopilotToken (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:552:56856)
    at V4.getCopilotToken (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:420:23138)
    at UE.waitForChatEnabled (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:420:25267)
    at UE.run (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:420:25151)
    at QL.askToUpgradeAuthPermissions (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:420:24809): Failed to get copilot token
2025-07-28 20:08:32.976 [error] You are not signed in to GitHub. Please sign in to use Copilot.
2025-07-28 20:08:32.976 [warning] [LanguageModelAccess] LanguageModel/Embeddings are not available without auth token
2025-07-28 20:08:32.976 [error] Error: GitHubLoginFailed
    at cO._authShowWarnings (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:552:58352)
    at cO.getCopilotToken (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:552:56856)
    at V4.getCopilotToken (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:420:23138)
    at WM._getAuthSession (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:549:4298)
    at Object.n [as task] (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:549:751)
    at Vm._processQueue (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:540:24688)
2025-07-28 20:08:32.976 [warning] [LanguageModelAccess] LanguageModel/Embeddings are not available without auth token
2025-07-28 20:08:32.976 [error] Error: GitHubLoginFailed
    at cO._authShowWarnings (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:552:58352)
    at cO.getCopilotToken (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:552:56856)
    at V4.getCopilotToken (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:420:23138)
    at WM._getAuthSession (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:549:4298)
    at t (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:549:3730)
    at WM._registerEmbeddings (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:549:4260)
    at async Promise.all (index 1)
    at async Promise.allSettled (index 1)
    at yC.waitForActivationBlockers (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:401:1247)
    at c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:401:2057
    at o9e (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.29.1\dist\extension.js:401:1837)
    at db.n (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:112:13386)
    at db.m (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:112:13349)
    at db.l (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:112:12805)
2025-07-28 20:08:32.977 [info] activationBlocker from 'languageModelAccess' took for 62ms
