@echo off
title Protected VSCode Launcher

REM 设置虚拟环境变量
set /a RANDOM_ID=%RANDOM% * 32768 + %RANDOM%
set VSCODE_MACHINE_ID=virtual_%RANDOM_ID%_%TIME:~6,2%
set MACHINE_ID=%VSCODE_MACHINE_ID%
set ELECTRON_MACHINE_ID=%VSCODE_MACHINE_ID%
set COMPUTERNAME=PC-%RANDOM%
set USERNAME=User%RANDOM%
set MAC_ADDRESS=02:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%:%RANDOM:~-2%

REM 启动 VSCode
start "" "C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe"

REM 脚本结束，环境变量只在 VSCode 进程中有效
